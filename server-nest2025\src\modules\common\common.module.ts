import { Module } from '@nestjs/common';
import { CommonController } from './common.controller';
import { CommonService } from './common.service';
import { SequelizeModule } from '@nestjs/sequelize';
import User from 'src/db/model/User';
import Party from 'src/db/model/Party';
import Venue from 'src/db/model/Venue';

@Module({
  controllers: [CommonController],
  providers: [CommonService],
  exports: [CommonService],
  imports: [
    SequelizeModule.forFeature([User]),
    SequelizeModule.forFeature([Party]),
    SequelizeModule.forFeature([Venue]),
  ],
})
export class CommonModule {} 