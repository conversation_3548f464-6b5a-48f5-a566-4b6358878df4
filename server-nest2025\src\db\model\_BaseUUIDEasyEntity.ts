import {
  Column,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ult,
  IsUUID,
  Table,
  DataType
} from 'sequelize-typescript';

export default class _BaseUUIDEasyEntity extends Model {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true
  })
  declare id: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW
  })
  declare createdAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW
  })
  declare updatedAt: Date;
}