import { Table, Column, Model, DataType, ForeignKey, BelongsTo } from 'sequelize-typescript';
import _BaseUUIDEntity from './_BaseUUIDEntity';
import User from './User';

@Table({
  timestamps: true
})
export default class UserApplyManager extends _BaseUUIDEntity {
  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '微信 openid'
  })
  declare openid: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '用户昵称'
  })
  declare nickName: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '用户头像'
  })
  declare avatar: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '申请类型'
  })
  declare type: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '申请内容'
  })
  declare content: string;

  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: '申请目标'
  })
  declare applyTarget: string;

  @Column({
    type: DataType.UUID,
    allowNull: false,
    comment: '运动项目 ID'
  })
  declare SportId: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.UUID,
    allowNull: true,
    comment: '用户 ID'
  })
  declare UserId: string;

  @BelongsTo(() => User)
  declare User: User;
} 