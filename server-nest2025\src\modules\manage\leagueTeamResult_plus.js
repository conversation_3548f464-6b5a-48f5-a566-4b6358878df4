module.exports = function (router, db, result) {

  router.post('/sport/leagueTeamResult', async (ctx, next) => {
    let item = {
      ...ctx.request.body
    };
    item = await db.LeagueTeamResult.create(item);
    ctx.body = result.success(item.id);
  })
  router.del('/sport/leagueTeamResult', async (ctx, next) => {
    let id = ctx.request.query.id;
    let item = await db.LeagueTeamResult.findByPk(id);
    if ((await db.MatchTeamResult.count({
        where: {
          LeagueId: item.LeagueId,
          TeamId: item.TeamId
        }
      })) > 0) {
      ctx.body = result.error('比赛中已经设置该队参与');
      return;
    }
    await item.destroy()
    ctx.body = result.success();
  })
}