module.exports = function (router, db, result) {
  /**
   * 获取一个meet
   */

  router.get('/sport', async (ctx, next) => {
    let d = await db.Sport.findOne({
      //   include: [
      //     {model:db.MeetTimePoint},
      //     {model:db.MeetLinkGroup}
      // ],
    });
    ctx.body = result.success(d);
  })
  /**
   * 修改meet
   * id
   */
  router.post('/sport', async (ctx, next) => {
    let id = ctx.request.body.id;
    let data = await db.Sport.findOne({
      where: {
        id
      }
    });

    Object.assign(data, ctx.request.body);

    await data.save();

    ctx.body = result.success();
  })
}