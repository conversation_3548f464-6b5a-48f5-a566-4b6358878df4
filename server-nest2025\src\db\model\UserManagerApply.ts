import {
  Column,
  DataType,
  Default,
  Model,
  Table,
} from 'sequelize-typescript';
import BaseEntity from './_BaseEntity';
@Table
export default class UserManagerApply extends BaseEntity {

    @Column
    declare content: string

    @Column
    declare bak: string

    @Column
    declare type: string

    @Column(DataType.UUID)
    declare LeagueId: string

    @Column
    declare SportId: string

    @Default(0)
    @Column
    declare state: number //0:申请中,1:驳回,2:通过

  // @ForeignKey(() => User)
  // @Column(DataType.UUID)
  // UserId: string
  // @BelongsTo(()=>User,{constraints:false})
  // User:User
}