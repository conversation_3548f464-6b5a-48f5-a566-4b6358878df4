// server-nest2025/src/db/relationship.ts

import Sequelize from 'sequelize';
import User from './model/User';
import UserGallery from './model/UserGallery';
import UserPost from './model/UserPost';
import UserLikeLog from './model/UserLikeLog';
import UserComment from './model/UserComment';
import Venue from './model/Venue';
import VenueStatic from './model/VenueStatic';
import VenueVisitLog from './model/VenueVisitLog';
import Sport from './model/Sport';
import League from './model/League';
import LeagueTeamResult from './model/LeagueTeamResult';
import Match from './model/Match';
import MatchClass from './model/MatchClass';
import MatchTeamResult from './model/MatchTeamResult';
import MatchTeamPlayer from './model/MatchTeamPlayer';
import Team from './model/Team';
import Player from './model/Player';
import TeamPlayer from './model/TeamPlayer';
import Account from './model/Account';
import Party from './model/Party';
import PartyStatic from './model/PartyStatic';
import PartyInterest from './model/PartyInterest';

// 其他模型如 ShareLog、ShareLogReceive、Document、Asset、WikiItem、VenueHistory 等如有需要可补充

export function associateAll(sequelize: Sequelize.Sequelize) {
  const models = sequelize.models;

  // 用户相关
  // UserGallery.belongsTo(User, { constraints: false });
  // UserPost.belongsTo(User, { constraints: false });
    //已有
  // UserLikeLog.belongsTo(User, { constraints: false });

  //已有
  // User.hasMany(UserComment, { constraints: false });
  // UserComment.belongsTo(User);

  // UserComment.belongsTo(UserComment, {
  //   foreignKey: 'parentId',
  //   targetKey: 'id',
  //   constraints: false,
  // });
  // UserComment.hasMany(UserComment, {
  //   foreignKey: 'parentId',
  //   as: 'children',
  // });

  // 场馆相关 已有
  // VenueVisitLog.belongsTo(Venue, { constraints: false });
  // VenueVisitLog.belongsTo(User, { constraints: false });
  // Venue.hasMany(VenueVisitLog, { constraints: false });


  //已有
  // Venue.hasOne(VenueStatic, {
  //   foreignKey: 'id',
  // });

  // Venue.belongsToMany(UserPost, { constraints: false, through: 'VenueUserPost' });
  // UserPost.belongsToMany(Venue, { constraints: false, through: 'VenueUserPost' });

  // 体育/赛事相关
  // Sport.hasMany(League);
  // Sport.hasMany(Team);
  // Sport.hasMany(Match);

  // League.hasMany(Match);
  // Match.belongsTo(League);

  // League.hasMany(LeagueTeamResult);
  // LeagueTeamResult.belongsTo(Team);
  // League.hasMany(MatchTeamResult);

  // MatchClass.belongsTo(Sport);

  // MatchClass.hasMany(Match);
  // Match.belongsTo(MatchClass);
  // Match.belongsTo(Sport);

  // MatchClass.belongsToMany(League, {
  //   through: 'LeagueMatchClass',
  // });
  // League.belongsToMany(MatchClass, {
  //   through: 'LeagueMatchClass',
  // });

  // Match.hasMany(MatchTeamResult);

  // MatchTeamResult.belongsTo(Team);
  // Match.hasMany(MatchTeamPlayer);
  // MatchTeamResult.belongsToMany(Player, {
  //   through: MatchTeamPlayer,
  // });
  // Player.belongsToMany(MatchTeamResult, {
  //   through: MatchTeamPlayer,
  // });

  // Team.belongsToMany(Player, {
  //   through: TeamPlayer,
  // });
  // Player.belongsToMany(Team, {
  //   through: TeamPlayer,
  // });

  // 使用模型名称字符串进行关联
  models.Account.belongsToMany(models.League, {
    through: 'LeagueAccount',
    foreignKey: 'AccountId',
  });
  models.League.belongsToMany(models.Account, {
    through: 'LeagueAccount',
    foreignKey: 'LeagueId',
  });

  // party相关
  // Party.hasOne(PartyStatic, {
  //   foreignKey: 'id',
  // });
  // PartyInterest.belongsTo(Party, { constraints: false });
  // PartyInterest.belongsTo(User, { constraints: false });
}