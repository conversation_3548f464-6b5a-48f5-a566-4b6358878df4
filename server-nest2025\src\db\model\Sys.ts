import {
  Column,
  DataType,
  Default,
  Table,
  Model,
  PrimaryKey
} from 'sequelize-typescript';
@Table({timestamps: false})
export default class Sys extends Model {
    @PrimaryKey
    @Column
    declare key: string

    @Column
    declare value: string

    @Column
    declare name: string

    @Column
    declare desc: string

    @Column
    declare type:number

    @Column
    declare index:number

    @Default(0)
    @Column
    declare disabled: number//0:可用
}
//系统配置
export const SysDict = {
  allowComment: {
    value: 0, //0:不可,1:可
    name: '评论许可',
    desc:JSON.stringify({'0':'停用','1':'启用'})
  },
  allowPushVenueByUser: {
    value: 0,//0:管理员身分提交,1:用户可提交
    name: '场所普通用户提交许可',
    desc:JSON.stringify({'0':'管理员身份','1':'用户身份'})
  },

  allowUserPost: {
    value: 0,
    name: '用户动态模块',
    desc:JSON.stringify({'0':'停用','1':'启用'})
  },
  allowUserGallery: {
    value: 0,
    name: '用户相册模块',
    desc:JSON.stringify({'0':'停用','1':'启用'})
  },
  msgCheck: {
    value: 1,
    name: '内容提交审核启用',
    desc:JSON.stringify({'0':'停用','1':'启用'})
  },
  allowPushParty: {
    value: 0,//0:管理员身分提交,1:用户可提交
    name: '约毽模块提交许可',
    desc:JSON.stringify({'0':'停用','1':'启用'})
  },
}