import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CityTeamController } from './cityTeam.controller';
import { CityTeamService } from './cityTeam.service';
import CityTeam from '../../db/model/CityTeam';
import User from '../../db/model/User';
import Sport from '../../db/model/Sport';

@Module({
  imports: [
    SequelizeModule.forFeature([CityTeam, User, Sport])
  ],
  controllers: [CityTeamController],
  providers: [CityTeamService],
  exports: [CityTeamService]
})
export class CityTeamModule {} 