import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber } from 'class-validator';

export class QueryMatchDto {
  @ApiPropertyOptional({ description: '联赛ID' })
  @IsOptional()
  @IsString()
  LeagueId?: string;

  @ApiPropertyOptional({ description: '比赛类别ID' })
  @IsOptional()
  @IsString()
  MatchClassId?: string;

  @ApiPropertyOptional({ description: '比赛类型' })
  @IsOptional()
  @IsNumber()
  type?: number;

  @ApiPropertyOptional({ description: '比赛状态' })
  @IsOptional()
  @IsNumber()
  state?: number;
} 