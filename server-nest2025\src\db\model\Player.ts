import {
  BelongsToMany,
  Column,
  DataType,
  Default,
  Table,
} from 'sequelize-typescript';
import  BaseUUIDEntity from './_BaseUUIDEntity';
import MatchTeamResult from './MatchTeamResult';
import Team from './Team';
import Sport from './Sport';
import MatchTeamPlayer from './MatchTeamPlayer';
import TeamPlayer from './TeamPlayer';

@Table
export default class Player extends BaseUUIDEntity {
  @Column 
    declare name: string
    @Column
    declare intro: string

    @Column
    declare avatar: string
    
    @Column(DataType.TEXT)
    declare content:string

    @Column
    declare bak: string

    @Column(DataType.STRING(1000))
    declare extUrl: string

    @Column
    declare openid: string;

    // @BelongsToMany(() => MatchTeamResult, () => MatchTeamPlayer)
    // MatchTeamResults: MatchTeamResult[];

    // @BelongsToMany(() => Team, () => TeamPlayer)
    // Teams: Team[];
}