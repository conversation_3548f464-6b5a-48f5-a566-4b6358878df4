import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsNotEmpty, IsNumber } from 'class-validator';

export class PostAlbumDto {
  @ApiProperty({ description: '场馆ID' })
  @IsString()
  @IsNotEmpty()
  venueId: string;

  @ApiProperty({ description: '图片列表' })
  @IsArray()
  @IsNotEmpty()
  photos: string[];

  @ApiProperty({ description: '内容' })
  @IsString()
  content: string;

  @ApiProperty({ description: '目标ID' })
  @IsString()
  targetId: string;

  @ApiProperty({ description: '父ID' })
  @IsString()
  parentId: string;

  @ApiProperty({ description: '深度' })
  @IsNumber()
  deepth: number;
} 