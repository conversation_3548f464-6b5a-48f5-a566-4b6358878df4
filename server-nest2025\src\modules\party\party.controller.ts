import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { PartyService } from './party.service';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserLevel, AccountType } from '../../utils/enums';
import { RequestUser } from '../../auth/decorators/requestUser.decorator';
import { CreatePartyDto } from './dto/create-party.dto';
import config from 'src/siteConfig';
import { ItemPartyDto } from './dto/item-party.dto';
import { pickDefinedFields } from 'src/utils/funs';

@Controller(`${config.API_PREFIX}/party`)
export class PartyController {
  constructor(private readonly partyService: PartyService) {}

  @Get()
  async getPartys(@Query() query: {divisionCode?: number,dateStart?: Date,dateEnd?: Date,order?: string,pageIndex?: number,pageSize?: number}, @RequestUser() user: any) {
    const result = await this.partyService.getPartys(query);
    result.list = result.list.map((item: any) => {
      item.isSelf = user && item.UserId === user.id;
      return pickDefinedFields<ItemPartyDto>(item);
    });
    return result;
  }

  @Get(':id')
  async getParty(@Param('id') id: string, @RequestUser() user: any) {
    return this.partyService.getParty(id, user);
  }

  @Post(':id/del')
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member, AccountType.Operator, AccountType.Admin)
  async deleteParty(@Param('id') id: string, @RequestUser() user: any) {
    return this.partyService.deleteParty(id, user);
  }

  @Post()
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  async createOrUpdateParty(@Body() dto: CreatePartyDto, @RequestUser() user: {id:string,ip:string}) {
    return this.partyService.createOrUpdateParty(dto, user);
  }

  @Post(':id/like')
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member, UserLevel.Vip)
  async likeParty(@Param('id') id: string, @RequestUser() user: any) {
    return this.partyService.likeParty(id, user);
  }

  @Get(':id/likes')
  async getPartyLikes(@Param('id') id: string, @Query() query: any) {
    return this.partyService.getPartyLikes(id, query);
  }

  @Post(':id/interest')
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member, UserLevel.Vip)
  async interestParty(@Param('id') id: string, @RequestUser() user: any) {
    return this.partyService.interestParty(id, user);
  }

  @Get(':id/interests')
  async getPartyInterests(@Param('id') id: string, @Query() query: any) {
    return this.partyService.getPartyInterests(id, query);
  }
} 