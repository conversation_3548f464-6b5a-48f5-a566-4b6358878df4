import {
  Column,
  DataType,
  Table,
  Model,
  PrimaryKey,
  AutoIncrement
} from 'sequelize-typescript';

@Table
export default class ShareLog extends Model {
    @Column
    declare creater: string

    @Column(DataType.UUID)
    declare targetId:string//目标ID

    @Column
    declare type:string//分享类型: venue

    @Column(DataType.STRING(1000))
    declare scene:number //分享参数
    
    @Column
    declare receiveCount:number //接收次数
}
