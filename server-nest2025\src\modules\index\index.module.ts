import { Module } from '@nestjs/common';
import { IndexController } from './index.controller';
import { IndexService } from './index.service';
import { db } from 'src/db/db';
import { SequelizeModule } from '@nestjs/sequelize';
import Sport from 'src/db/model/Sport';
import MatchClass from 'src/db/model/MatchClass';
import Asset from 'src/db/model/Asset';
import Sys from 'src/db/model/Sys';
import League from 'src/db/model/League';
import Match from 'src/db/model/Match';
import MatchTeamResult from 'src/db/model/MatchTeamResult';
import LeagueTeamResult from 'src/db/model/LeagueTeamResult';
import Team from 'src/db/model/Team';
import Venue from 'src/db/model/Venue';
import User from 'src/db/model/User';
import CityTeam from 'src/db/model/CityTeam';

@Module({
  imports: [
    db,
    SequelizeModule.forFeature([
      Sport,
      MatchClass,
      Asset,
      Sys,
      League,
      Match,
      MatchTeamResult,
      LeagueTeamResult,
      Team,
      Venue,
      User,
      CityTeam,
    ]),
  ],
  controllers: [IndexController],
  providers: [IndexService],
  exports: [IndexService],
})
export class IndexModule {}
