import {
  Column,
  <PERSON>T<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Table,
} from 'sequelize-typescript';
import  BaseUUIDEntity from './_BaseUUIDEntity';
import League from './League';
import Team from './Team';
import Match from './Match';
@Table

export default class Sport extends BaseUUIDEntity {
    @Column
    declare name: string

    @Column
    declare intro: string

    @Column
    declare avatar: string

    @Column
    declare img: string

    @Column
    declare code: string

    @Column(DataType.STRING(1000))
    declare extUrl: string

    @HasMany(() => League, { constraints: false })
    declare Leagues: League[]

    @HasMany(() => Team, { constraints: false })
    declare Teams:Team[]

    @HasMany(() => Match, { constraints: false })
    declare Matches:Match[]

}