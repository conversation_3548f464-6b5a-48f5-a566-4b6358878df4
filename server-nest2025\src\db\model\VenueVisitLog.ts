import {
  Column,
  DataType,
  Model,
  Table,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import User from './User';
import Venue from './Venue';

@Table
export default class VenueVisitLog extends Model {

  @Column
  declare lat: string //地址坐标

  @Column
  declare lon: string //地址坐标,
  
  @Column
  declare name: string

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  declare UserId: string
  @BelongsTo(() => User, { constraints: false, foreignKey: 'UserId' })
  declare User: User;

  @ForeignKey(() => Venue)
  @Column(DataType.UUID)
  declare VenueId: string
  
  @BelongsTo(() => Venue, { constraints: false, foreignKey: 'VenueId' })
  declare Venue: Venue
}
