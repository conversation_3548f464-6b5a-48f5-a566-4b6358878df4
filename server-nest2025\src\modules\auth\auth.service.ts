import { ForbiddenException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Op } from "sequelize";
import Account from "src/db/model/Account";
import User from "src/db/model/User";

interface CheckRoleOptions {
  levels?: number[];
  types?: number[];
}

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Account) private accountModel: typeof Account,
  ) {}

  public async checkRole(userId: string, options: CheckRoleOptions = {}, errorMsg?: string): Promise<boolean> {
    let user: User | null = null;

    if (options.levels && options.levels.length > 0) {
      user = await this.userModel.findOne({
        where: {
          id: userId,
          level: { [Op.or]: options.levels }
        }
      });
    } else if (options.types && options.types.length > 0) {
      user = await this.userModel.findOne({
        where: { id: userId },
        include: [{
          model: this.accountModel,
          required: true,
          where: {
            type: { [Op.or]: options.types }
          }
        }]
      });
    } else {
      user = await this.userModel.findByPk(userId);
    }

    if (user) {
      return true;
    } else {
      throw new ForbiddenException(errorMsg || '权限错误');
    }
  }
}