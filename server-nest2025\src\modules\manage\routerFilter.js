const result = require('../../utils/jsonResult');
const AccountType = {
    '超级管理员': 999,
    '平台管理员': 910,
    '赛季管理员': 110,
    '赛季记分员': 105,//未用
}
const ControllType = {
    '大于': 1,
    '小于': 2,
    '大于等于': 3,
    '小于等天': 4,
    '等于': 5
}
module.exports = {
    AccountType,
    ControllType,
    Factory(allowAccountType, controllType = ControllType.小于) {
        return async function (ctx, next) {
            const account = ctx.params.account;
            if (!account.id) {
                throw new myError('请先登录', 102);
            }
            let allow = false;
            switch (controllType) {

                case ControllType.大于:
                    if (account.type < allowAccountType) {
                        allow = true;
                    }
                    break;
                case ControllType.小于:
                    if (account.type > allowAccountType) {
                        allow = true;
                    }
                    break;
                case ControllType.大于等于:
                    if (account.type <= allowAccountType) {
                        allow = true;
                    }
                    break;
                case ControllType.小于等天:
                    if (account.type >= allowAccountType) {
                        allow = true;
                    }
                    break;
                case ControllType.等于:
                    if (account.type == allowAccountType) {
                        allow = true;
                    }
                    break;

            }
            if (allow) {
                return next()
            } else {
                ctx.body = result.error('无权限', -103)
            }
        }
    }
}