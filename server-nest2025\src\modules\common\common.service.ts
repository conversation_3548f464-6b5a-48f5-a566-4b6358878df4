import { Injectable } from '@nestjs/common';
import * as qiniu from 'qiniu';
import siteConfig from '../../siteConfig';
import chinaDivision from '../../utils/china-division.json';

@Injectable()
export class CommonService {
  getQnToken(): string {
    const accessKey = siteConfig.QINIU_ACCESS_KEY!;
    const secretKey = siteConfig.QINIU_SECRET_KEY!;
    const mac = new qiniu.auth.digest.Mac(accessKey, secretKey);
    const options = { scope: 'jianqiu' };
    const putPolicy = new qiniu.rs.PutPolicy(options);
    return putPolicy.uploadToken(mac);
  }

  async getLocationByIp(ip: string) {
    try {
      // 简单的IP地理位置获取逻辑
      // 这里可以根据实际需求调用第三方API或使用本地IP库
      
      // 示例：根据IP段判断地理位置（实际项目中建议使用专业的IP地理位置服务）
      const location = await this.getLocationFromIp(ip);
      
      // 提取省市县信息，提供默认值
      const country = location?.country || '';
      const province = location?.province || '';
      const city = location?.city || '';
      const district = location?.district || '';
      
      return { 
        country,
        province, 
        city, 
        district,
        fullAddress: [province, city, district].filter(Boolean).join(' ')
      };
    } catch (error) {
      // 如果获取失败，返回默认值
      console.error('获取IP位置信息失败:', error);
      return {
        province: '',
        city: '',
        district: '',
        fullAddress: ''
      };
    }
  }

  private async getLocationFromIp(ip: string) {
    // 这里可以实现具体的IP地理位置获取逻辑
    // 例如调用第三方API：ip-api.com, ipinfo.io 等
    
    // 示例实现（实际项目中需要替换为真实的API调用）
    if (ip === '127.0.0.1' || ip === 'localhost') {
      return {
        province: '本机',
        city: '本机',
        country: '本机'
      };
    }
    
    //这里可以添加真实的IP地理位置API调用
    const response = await fetch(`http://ip-api.com/json/${ip}?lang=zh-CN`);
    const data = await response.json();
    return {
      country: data.country,
      province: data.regionName,
      city: data.city,
      district: data.district
    };
  }
  /**
   * 尝试获取divisionCode 0:获取失败
   * @param province 省份
   * @param city 城市
   * @param area 区县
   * @returns divisionCode
   */
  tryGetDivisionCode(province: string, city: string, area?: string):number {

    let divisionCityItem = chinaDivision.data.find( 
      item => (item.ns &&item.ns === province) || item.n === province
    )?.c.find(
      cityItem =>  (cityItem.ns && cityItem.ns === city) || cityItem.n === city
    );
    if(!divisionCityItem){
      return 0;
    }
    if(area){
      const divisionAreaItem = divisionCityItem.c.find(areaItem => areaItem.n === area);
      if(!divisionAreaItem){
        return 0;
      }
      return Number(divisionAreaItem.v);
    }
    return Number(divisionCityItem.v);
  }
} 