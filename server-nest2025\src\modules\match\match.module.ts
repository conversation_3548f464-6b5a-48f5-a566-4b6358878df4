import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { MatchController } from './match.controller';
import { MatchService } from './match.service';
import Match from '../../db/model/Match';
import MatchTeamResult from '../../db/model/MatchTeamResult';
import Team from '../../db/model/Team';
import League from '../../db/model/League';
import MatchClass from '../../db/model/MatchClass';
import Account from '../../db/model/Account';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Match,
      MatchTeamResult,
      Team,
      League,
      MatchClass,
      Account
    ]),
  ],
  controllers: [MatchController],
  providers: [MatchService],
  exports: [MatchService]
})
export class MatchModule {}