import { Injectable, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import Account from '../../db/model/Account';
import League from '../../db/model/League';
import User from '../../db/model/User';
import { AccountDto } from './dto/account.dto';
import { Op } from 'sequelize';
import { cryptPwd } from '../../utils/lib';

@Injectable()
export class AccountService {
  constructor(
    @InjectModel(Account) private accountModel: typeof Account,
    @InjectModel(League) private leagueModel: typeof League,
    @InjectModel(User) private userModel: typeof User,
  ) {}

  async getAccounts(query: any) {
    const { pageIndex = 1, pageSize = 20 } = query;
    const offset = (pageIndex - 1) * pageSize;
    const { count, rows } = await this.accountModel.findAndCountAll({
      include: [
        { model: League, attributes: ['id', 'name'] },
        { model: User, attributes: ['id', 'openid'] }
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: +pageSize,
    });
    return { list: rows, total: count, pageSize: +pageSize, pageIndex: +pageIndex };
  }

  async createOrUpdateAccount(dto: AccountDto, operator: Account) {
    if (operator.type < 900) throw new ForbiddenException('无权限');
    if (!dto.name || !dto.type || isNaN(dto.type)) throw new BadRequestException('参数不正确');
    if ((!dto.id && !dto.password) || (dto.id && dto.password === "")) throw new BadRequestException('必须提供密码');
    if (operator.type != 999 && operator.type >= dto.type) throw new BadRequestException('无法创建比自身权限高的账户');
    if (dto.password !== undefined && dto.password.length < 6) throw new BadRequestException('密码至少六位');

    let item: Account|null;
    if (dto.id) {
      item = await this.accountModel.findByPk(dto.id);
      if (!item) throw new BadRequestException('账号不存在');
      if (!dto.password) {
        delete dto.password;
      } else {
        item.openid = "";
        dto.password = cryptPwd(dto.password);
      }
      Object.assign(item, dto);
      await item.save();
    } else {
      delete dto.id;
      if (dto.password) dto.password = cryptPwd(dto.password);
      item = await this.accountModel.create(dto as any);
    }

    // 处理 leagueIds
    if (dto.type > 200 && dto.type < 300 && dto.leagueIds !== undefined) {
      const leagues = await this.leagueModel.findAll({
        where: {
          [Op.or]: dto.leagueIds.split(",").map(id => ({ id }))
        }
      });
      await item.$set('Leagues' as keyof Account, leagues);
    } else if (dto.leagueIds === "") {
      await item.$set('Leagues' as keyof Account, []);
    }

    return item.id;
  }

  async unBindAccount(id: string) {
    const item = await this.accountModel.findByPk(id);
    if (!item) throw new BadRequestException('账号不存在');
    item.openid = "";
    await item.save();
    return item.id;
  }

  async deleteAccount(id: string, operator: Account) {
    if (operator.type < 900) throw new ForbiddenException('无权限');
    const item = await this.accountModel.findByPk(id);
    if (!item) throw new BadRequestException('账号不存在');
    await item.destroy();
    return true;
  }
}