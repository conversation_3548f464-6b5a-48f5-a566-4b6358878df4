import { IsString, IsOptional, IsNumber } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCommentDto {
  @ApiProperty({ description: '目标ID' })
  @IsString()
  targetId: string;

  @ApiProperty({ description: '评论内容' })
  @IsString()
  content: string;

  @ApiProperty({ description: '父评论ID', required: false })
  @IsOptional()
  @IsString()
  parentId?: string;

  @ApiProperty({ description: '评论深度', required: false, default: 0 })
  @IsOptional()
  @IsNumber()
  deepth?: number = 0;
}