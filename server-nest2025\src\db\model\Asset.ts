import {
  Column,
  DataType,
  Table,
} from "sequelize-typescript";
import Base<PERSON>UIDEntity  from "./_BaseUUIDEntity";
@Table
export default class Asset extends BaseUUIDEntity{
    @Column
    declare name: string

    @Column
    declare type: string //banner,img

    @Column
    declare intro: string //简短介绍

    @Column(DataType.STRING(1000))
    declare content:string  //json imgs

    @Column
    declare key: string

    @Column
    declare group: string

    @Column(DataType.UUID)
    declare SportId: string

}