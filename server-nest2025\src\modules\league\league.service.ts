import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import League from '../../db/model/League';
import MatchClass from '../../db/model/MatchClass';
import { IndexService } from '../index/index.service';

@Injectable()
export class LeagueService {
  constructor(
    @InjectModel(League)
    private leagueModel: typeof League,
    @InjectModel(MatchClass)
    private matchClassModel: typeof MatchClass,
    private indexService: IndexService,
  ) {}

  async getLeagues() {
    const cacheData = this.indexService.getCacheData();
    const leagues = await this.leagueModel.findAll({
      where: {
        SportId: cacheData.SportId
      },
      attributes: [
        'id', 'name', 'state', 'intro', 'rank', 'avatar', 
        'beginDate', 'endDate', 'matchOverCount', 'matchCount', 'bak1'
      ],
      order: [
        ['rank', 'DESC']
      ],
      include: [{
        model: this.matchClassModel,
        attributes: ['name', 'avatar']
      }],
      raw:true,
      nest:true
    });
    //console.log(leagues);
    return leagues;
  }

  async getLeague(id: string) {
    const league = await this.leagueModel.findOne({
      where: { id },
      include: [{
        model: this.matchClassModel,
        attributes: ['name', 'avatar']
      }],
      raw:true,
      nest:true
    });

    return league;
  }
} 