import { Column, Model, DataType, Table, BelongsTo, BelongsToMany, ForeignKey } from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import Match from './Match';
import Team from './Team';
import League from './League';
import Player from './Player';
import MatchTeamPlayer from './MatchTeamPlayer';

@Table({
  timestamps: true
})
export default class MatchTeamResult extends BaseUUIDEntity {
  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '0:未开始,1:进行中,2:结束'
  })
  declare state: number;

  @Column({
    type: DataType.DECIMAL,
    defaultValue: 0.0
  })
  declare score: number;

  @Column(DataType.STRING)
  declare scoreDetails: string;

  @Column(DataType.STRING)
  declare bak: string;

  @Column(DataType.STRING(1000))
  declare content: string;

  @Column({
    type: DataType.STRING,
    comment: '文字数据关系 sport_league_match'
  })
  declare attLink: string;

  // 关联字段
  @ForeignKey(() => Match)
  @Column(DataType.UUID)
  declare MatchId: string;

  @ForeignKey(() => Team)
  @Column(DataType.UUID)
  declare TeamId: string;

  @ForeignKey(() => League)
  @Column(DataType.UUID)
  declare LeagueId: string;

  // 关联关系
  @BelongsTo(() => Match)
  declare Match: Match;

  @BelongsTo(() => Team)
  declare Team: Team;

  @BelongsTo(() => League)
  declare League: League;

  // @BelongsToMany(() => Player, () => MatchTeamPlayer)
  // Players: Player[];
} 