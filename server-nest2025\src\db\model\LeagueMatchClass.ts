import { Column, DataType, ForeignKey, Model, Table } from 'sequelize-typescript';
import League from './League';
import MatchClass from './MatchClass';

@Table({ tableName: 'LeagueMatchClass', timestamps: false })
export default class LeagueMatchClass extends Model {
  @ForeignKey(() => MatchClass)
  @Column(DataType.UUID)
  declare MatchClassId: string;

  @ForeignKey(() => League)
  @Column(DataType.UUID)
  declare LeagueId: string;
}