import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam } from '@nestjs/swagger';
import { TeamService } from './team.service';

@ApiTags('团队')
@Controller('client/team')
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  @ApiOperation({ summary: '获取团队列表' })
  @Get('teams')
  async getTeams() {
    return await this.teamService.getTeams();
  }

  @ApiOperation({ summary: '获取团队详情' })
  @ApiParam({ name: 'id', description: '团队ID' })
  @Get(':id')
  async getTeam(@Param('id') id: string) {
    return await this.teamService.getTeam(id);
  }
} 