const {parseQueryPaging} = require("./../../utils/funs.js")
const Sequelize = require('sequelize');

module.exports = function (router, db, result) {

  router.get('/sport/league/teams', async (ctx, next) => {
    let paging = parseQueryPaging(ctx.request.query);


    let league = await db.League.findOne({
      where: {
        id: ctx.request.query.LeagueId
      },
      include: [{
        model: db.LeagueTeamResult,
        attributes: ['id', 'score', 'rank'],
        include: [{
          model: db.Team,
          attributes: ['id', 'name', 'avatar']
        }]
      }],
    })
    let teams = league.LeagueTeamResults;
    ctx.body = result.success({
      list: teams,
      total: teams.length,
      pageSize: paging.pageSize,
      pageIndex: paging.pageIndex
    });

    
  })



  // router.post('/sport/league/updateMatchState', async (ctx, next) => {
  //   let LeagueId = ctx.request.body.LeagueId;
  //   let matchs = await db.Match.findAll({
  //     where: {
  //       disabled: 0,
  //       LeagueId,
  //     }
  //   });
  //   let matchOverCount = matchs.filter(v => v.state == 2).length;
  //   let matchCount = matchs.length

  //   await db.League.update({
  //     matchOverCount,
  //     matchCount
  //   }, {
  //     where: {
  //       id: LeagueId
  //     }
  //   });
  //   ctx.body = result.success();

  // })


}