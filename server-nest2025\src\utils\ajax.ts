'use strict';
import * as request from 'request';

interface AjaxConfig {
  url: string;
  data?: Record<string, any>;
  method?: 'GET' | 'POST';
  encoding?: string;
  headers?: Record<string, string>;
  timeout?: number;
  successFunc?: (data: any) => void;
  errorFunc?: (error: any) => void;
}

const defaultConfig: AjaxConfig = {
  url: '',
  data: {},
  method: 'GET',
  encoding: 'utf-8',
  headers: {},
  timeout: 10,
  successFunc: () => {},
  errorFunc: () => {},
};

export const ajax = (config: AjaxConfig): void => {
  if (!config || typeof config !== 'object') {
    console.log('params error');
    return;
  }

  if (!config.url) {
    console.log('url cannot be null or undefined');
    return;
  }

  // 合并配置
  const finalConfig = { ...defaultConfig, ...config };

  // 构建请求选项
  const options: request.Options = {
    url: finalConfig.url,
    method: finalConfig.method,
    headers: finalConfig.headers,
    timeout: (finalConfig.timeout ?? 10) * 1000,
    encoding: finalConfig.encoding,
  };

  // 处理请求数据
  if (finalConfig.method === 'GET') {
    options.qs = finalConfig.data;
  } else if (finalConfig.method === 'POST') {
    options.form = finalConfig.data;
  }

  // 发送请求
  request(options, (error, response, body) => {
    if (error) {
      finalConfig.errorFunc?.(error);
      return;
    }

    if (response.statusCode !== 200) {
      finalConfig.errorFunc?.(new Error(`HTTP Error: ${response.statusCode}`));
      return;
    }

    try {
      const data = typeof body === 'string' ? JSON.parse(body) : body;
      finalConfig.successFunc?.(data);
    } catch (e) {
      finalConfig.errorFunc?.(e);
    }
  });
};

module.exports = ajax;
