import crypto from 'crypto';
import * as request from 'request';
import siteConfig from '../siteConfig';

interface WxTokenResponse {
  access_token: string;
  expires_in: number;
}

interface WxSessionResponse {
  openid: string;
  session_key: string;
  errcode?: number;
  errmsg?: string;
}

interface WxDecryptedData {
  watermark: {
    appid: string;
  };
  [key: string]: any;
}


const appId = siteConfig.WXAPP_ID||'';
const secret = siteConfig.WXAPP_SECRET||'';

let access_token = '';
let access_token_expireTime: number = 0;

export const getAccessToken = async (): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (access_token_expireTime !== 0 && access_token && new Date().getTime() < access_token_expireTime) {
      resolve(access_token);
    } else {
      request.get(
        `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${secret}`,
        (err, res, data) => {
        if (res && res.statusCode === 200) {
            const jData: WxTokenResponse = JSON.parse(data);
          access_token = jData.access_token;
            access_token_expireTime = new Date().getTime() + jData.expires_in * 1000;
            resolve(access_token);
        } else {
            reject(err);
          }
        }
      );
    }
  });
};

//wx用户信息解密 by encryptedData，2023.9.20已确认无法再获得用户信息，解出来也是默认值，只能用wx.login code换取openid，除了头像其它信息需要自己填写完善
export const decryptData = (_encryptedData: string, _iv: string, _sessionKey: string): WxDecryptedData => {
  const iv = Buffer.from(_iv, 'base64');
  const encryptedData = Buffer.from(_encryptedData, 'base64');
  const sessionKey = Buffer.from(_sessionKey, 'base64');

  let decoded: WxDecryptedData;
  try {
    const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKey, iv);
    decipher.setAutoPadding(true);
    let decrypted = decipher.update(encryptedData, undefined, 'utf8');
    decrypted += decipher.final('utf8');
    decoded = JSON.parse(decrypted);
  } catch (e) {
    throw new Error('Illegal Buffer');
    }
    
    if (decoded.watermark.appid !== appId) {
    throw new Error('Illegal Buffer');
      }
  
  return decoded;
};
  
//应该不需要了，前段wx.login可以拿到openid
export const getOpenidAsync = async (code: string): Promise<WxSessionResponse> => {
  const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${siteConfig.WXAPP_ID}&secret=${siteConfig.WXAPP_SECRET}&js_code=${code}&grant_type=authorization_code`;
  return new Promise((resolve, reject) => {
      request.get(url, (err, res, data) => {
        if (res && res.statusCode === 200) {
        const response: WxSessionResponse = JSON.parse(data);
        if (response.errcode) {
          reject(new Error(response.errmsg));
        } else {
          resolve(response);
        }
      } else {
        reject(err);
        }
    });
  });
};

export { appId, secret };