import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam } from '@nestjs/swagger';
import { LeagueService } from './league.service';
import config from 'src/siteConfig';

@ApiTags('联赛')
@Controller(`${config.API_PREFIX}/league`)
export class LeagueController {
  constructor(private readonly leagueService: LeagueService) {}

  @ApiOperation({ summary: '获取联赛列表' })
  @Get('')
  async getLeagues() {
    return await this.leagueService.getLeagues();
  }

  @ApiOperation({ summary: '获取联赛详情' })
  @ApiParam({ name: 'id', description: '联赛ID' })
  @Get(':id')
  async getLeague(@Param('id') id: string) {
    return await this.leagueService.getLeague(id);
  }
}