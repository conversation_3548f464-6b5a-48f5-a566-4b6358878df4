/*
openid对应到account表openid
是否需要当user 与 account做关系(1 v 1)映射的思考 2020.22.12：
好处：查询方法，结构清晰，可不必使用openid换成userId更科学
害处：改成大，耦合强
如果增加 user.AccountId，方便使用，但实用性不大，且在没有级联的情况下，与account表同步会比较麻烦
*/
import {
  BelongsTo,
  Column,
  DataType,
  Default,
  HasMany,
  Table,
} from 'sequelize-typescript';
import UserComment from './UserComment';
import Party from './Party';
import Account from './Account';
import _BaseUUIDEasyEntity from './_BaseUUIDEasyEntity';

@Table
export default class User extends _BaseUUIDEasyEntity {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true
  })
  declare id: string;

    @Column
    declare name: string

    @Column
    declare openid: string

    @Column
    declare nickName: string

    @Column
    declare avatar: string

    @Column
    declare from: string //wx,

    @Column
    declare gender: string

    @Column
    declare sign?: string//签名

    @Column
    declare phone?:string//电话

    @Column
    declare bak?: string

    @Column
    declare address?: string

    @Column
    declare country?: string

    @Column
    declare city?: string

    @Column
    declare province?: string

    @Column
    declare divisionCode?: number

    @Column
    declare lat?: string //地址坐标

    @Column
    declare lon: string //地址坐标

    @Column
    declare ip: string //ip

    @Default(0)
    @Column
    declare level:number
    
    @Default(0)
    @Column
    declare state: number

    @HasMany(() => UserComment, { constraints:false })
    declare Comments: UserComment[]

    @HasMany(() => Party, { foreignKey: 'UserId', constraints:false })
    declare Parties: Party[];

    @BelongsTo(() => Account, { foreignKey: 'openid', constraints:false, targetKey: 'openid' })
    declare Account: Account;
}