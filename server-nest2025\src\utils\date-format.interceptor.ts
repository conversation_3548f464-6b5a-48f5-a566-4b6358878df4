import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { format } from 'date-fns';

function isDateString(val: any): boolean {
    // 简单判断 yyyy-MM-dd 或 yyyy/MM/dd 或 ISO 字符串
    return typeof val === 'string' && /^\d{4}[-/]\d{2}[-/]\d{2}/.test(val);
  }
  
  function formatDates(obj: any): any {
    if (obj instanceof Date) {
      return format(obj, 'yyyy/MM/dd');
    }
    if (isDateString(obj)) {
      // 只要是日期字符串也格式化
      return format(new Date(obj), 'yyyy/MM/dd');
    }
    if (Array.isArray(obj)) {
      return obj.map(formatDates);
    }
    if (obj && typeof obj === 'object') {
      const result = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          result[key] = formatDates(obj[key]);
        }
      }
      return result;
    }
    return obj;
  }

@Injectable()
export class DateFormatInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(map(data => formatDates(data)));
  }
}