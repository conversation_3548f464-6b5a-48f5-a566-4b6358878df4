import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import siteConfig from '../../siteConfig';
import { AccountType, UserLevel } from 'src/utils/enums';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
  ) {
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    
    // 1. 验证请求头中的 _s 字段
    const _s = request.headers['_s'];
    if (_s !== 'jianqiu') {
      throw new UnauthorizedException('Invalid request');
    }

    // 2. 获取 token（支持 header 和 query 两种方式）
    const token = this.extractToken(request);
    
    // 3. 设置默认用户信息
    let user:ITokenUser = { id: '', openid: '',ip:'',type:'',level:''};
    
    if (token) {
      // 开发环境，使用默认用户信息
      if(token=='dev.windbell2'){
        user = {
          openid: 'oReqf4lWkhKJq4qMry4FYwdAs2Y4',
          id: '67c98c10-5894-11ee-b58e-b7049711eb82',
          ip: request.ip,
          type:AccountType.Admin,
          level:UserLevel.Admin
        };

      } else {
        try {
          // 4. 使用 JwtService 验证 token
          const payload = await this.jwtService.verifyAsync(token, {
            secret: siteConfig.JWT_SECRET
          });
          
          if (payload && payload.exp > Date.now() / 1000) {
            user = {
              openid: payload.openid,
              id: payload.id,
              ip: request.ip,
              type:payload.type as AccountType,
              level:payload.level as UserLevel
            };
          }
        } catch (error) {
          // token 验证失败，使用默认用户信息
        }
      }
    }
    user.ip = request.ip;
    // 5. 将用户信息附加到请求对象上
    request.user = user;
    return true;
  }

  private extractToken(request: Request): string | undefined {
    // 从 header 获取
    const token = request.headers['token'];
    if (token) {
      return token as string;
    }
    
    // 从 query 获取
    return request.query['token'] as string;
  }
}