const {getNameComplex,queryPaginDataAsync} = require("./../../utils/funs.js")
const {AccountType} = require("./../../utils/lib.js")
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

module.exports = function (router, db, result, modelName = "UserApplyManager", prefixPath = "/user") {

  let ModelName_complex = getNameComplex(modelName);

  router.get(`${prefixPath}/${ModelName_complex}`, async (ctx, next) => {
    let data = await queryPaginDataAsync(db.UserApplyManager,ctx.query,{
      order: [
        ['createdAt', 'DESC']
      ],
      
      include: [{
        association: db.UserApplyManager.belongsTo(db.Account, {
          foreignKey: 'openid',
          targetKey: 'openid'
        }),
      }]
    })

    ctx.body = result.success(data);

    
  })

  /**
   * edit 
   */
  router.post(`${prefixPath}/${modelName.toLowerCase()}`, async (ctx, next) => {
    let item = {
      ...ctx.request.body
    };


    let _item = await db[modelName].findOne({
      where: {
        id: item.id
      }
    });
    Object.assign(_item, item)
    await _item.save();
    item = _item;


    ctx.body = result.success(item.id);
  })
  /**
   * del 
   */
  router.delete(`${prefixPath}/${modelName.toLowerCase()}`, async (ctx, next) => {
    let id = ctx.request.query.id;


    let item = await db[modelName].findByPk(id);
    await item.destroy()


    ctx.body = result.success();


  })

  router.get(`${prefixPath}/unBindAccounts`, async (ctx, next) => {

    let d = await db.Account.findAll({
      where: {
        openid: "",
        // type: {
        //   [Op.gt]: AccountType.Admin
        // }
      }
    })
    ctx.body = result.success(d);
  })

  router.post(`${prefixPath}/BindAccount`, async (ctx, next) => {
    const operator = ctx.params.account;

    const accountId = ctx.request.body.accountId;
    const openid = ctx.request.body.openid;
    const applyId = ctx.request.body.applyId;

    let account = await db.Account.findByPk(accountId);
    if (operator.type < account.type) {
      ctx.body = result.error('无权限');
      return;
    }
    account.openid = openid;
    await account.save()

    let apply = await db.UserApplyManager.findByPk(applyId);
    apply.state = 1;
    await apply.save()
    ctx.body = result.success();
  })


}