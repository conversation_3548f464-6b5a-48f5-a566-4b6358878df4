import { ApiProperty } from '@nestjs/swagger';

export class ItemPartyDto {
  @ApiProperty({ description: '活动ID', required: false })
  id?: string;

  @ApiProperty({ description: '标题' })
  name: string;

  @ApiProperty({ description: '内容' })
  content: string;

  @ApiProperty({ description: '开始时间' })
  beginDate: string;

  @ApiProperty({ description: '区划代码', required: false })
  divisionCode: number;

  @ApiProperty({ description: '地址', required: false })
  address?: string;

  @ApiProperty({ description: '经度', required: false })
  lon?: string;

  @ApiProperty({ description: '纬度', required: false })
  lat?: string;

  @ApiProperty({ description: '活动类型', required: false })
  type?: number;

  @ApiProperty({ description: '规模', required: false })
  size?: number;

  @ApiProperty({ description: '活动用时', required: false })
  time?: number;

  @ApiProperty({ description: '停车情况', required: false })
  parking?: number;

  @ApiProperty({ description: '付费情况', required: false })
  paid?: number;

  @ApiProperty({ description: '场地类型', required: false })
  venueType?: number;

  @ApiProperty({ description: '联系人', required: false })
  contacter?: string;

  @ApiProperty({ description: '联系人电话', required: false })
  contacterPhone?: string;

  @ApiProperty({ description: '场地ID', required: false })
  VenueId?: string;

  @ApiProperty({ description: '用户ID', required: false })
  UserId?: string;

  @ApiProperty({ description: '用户', required: false })
  User:{
    id:string,
    nickName:string,
    avatar:string
  }

  @ApiProperty({ description: '是否是自己创建的', required: false })
  isSelf?: boolean;

  
} 