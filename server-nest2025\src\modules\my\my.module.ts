import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { MyController } from './my.controller';
import { MyService } from './my.service';
import UserPost from '../../db/model/UserPost';
import UserGallery from '../../db/model/UserGallery';
import VenueVisitLog from '../../db/model/VenueVisitLog';
import UserLikeLog from '../../db/model/UserLikeLog';
import Venue from '../../db/model/Venue';
import { IndexModule } from '../index/index.module';

@Module({
  imports: [
    SequelizeModule.forFeature([UserPost, UserGallery, VenueVisitLog, UserLikeLog, Venue]),
    IndexModule
  ],
  controllers: [MyController],
  providers: [MyService],
})
export class MyModule {} 