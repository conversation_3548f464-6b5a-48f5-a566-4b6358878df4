/**球队类型 二进制多选 前四位保留扩展使用
 *  0b00000001 小花 0b00000010 大白 0b00000100 网毽 0b00001000 比赛 
*/
export enum CityTeamType {
  小花 = 0b00000001 ,
  大白 = 0b00000010,
  网毽 = 0b00000100,
  比赛 = 0b00001000,
}

export enum MatchType {
  单人 = 1,
  单人对抗 = 2,
  团体 = 3,
  团体对抗 = 4
}

export enum State {
  未开始 = 0,
  进行中 = 1,
  已结束 = 2
}

export enum PartyPaid {
  免费 = 0,
  有费用 = 1,
}

export enum PartySize {
  "人数不限" = 0,
  '3-5人' = 1,
  '6-10人' = 2,
  '11-20人' = 3,
}

export enum PartyType {
  '小花' = 0,
  '大白' = 1,
  '网毽' = 2,
  '比赛' = 3,
}
export enum PartyTime {
  '无时长' = 0,
  '1-2小时' = 1,
  '3-5小时' = 2
}

export enum VenuePaid {
  免费 = 1,
  收费 = 2,
  多样 = 3,
}
export enum VenueSize {
  多场地 = 2,
  单场地 = 3,
  综合 = 1,
}
export enum VenueType {
  户外 = 1,
  室内 = 2,
  综合 = 3,
}
export enum VenueAble {

  白天 = 2,
  夜晚 = 3,
  全天 = 1,
}
export enum VenueParking {
  无 = 1,
  一些 = 2,
  充足 = 3,
}


export enum UserLevel  {
  'Default'=0,//缺省
  'Member'=1,//注册用户
  'Vip'= 2,//会员
  'Admin'= 3,//管理员
}

export enum AccountType  {
  Admin= 999,//超级管理员
  Operator=910,//平台运维人员
  LeagueManager= 110,// 赛季维护
  LeagueOperator=105//未用
}

export enum CommentType{
  Comment=1,
  Venue=2,
  Party=3,
}

export enum LikeType{
  Comment=1,
  Venue=2,
  Party=3,
}