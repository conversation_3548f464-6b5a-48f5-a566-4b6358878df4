const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const {queryPaginDataAsync,getNameComplex} = require("./../../utils/funs.js")


module.exports = function (router, db, result, modelName, prefixPath, attendModels = {
  list: [],
  get: [],
  post: [], //对一关联
  postList: [], //对多关联
}) {
  //复数
  let ModelName_complex = getNameComplex(modelName);

  /**
   * get list
   */
  router.get(`${prefixPath}/${ModelName_complex.toLowerCase()}`, async (ctx, next) => {

    // let paging = parseQueryPaging(ctx.request.query);
    // let where = parseQueryParam(ctx.request.query)

    // let options = {
    //   where: where,
    //   order: [
    //     ['rank', 'DESC']
    //   ],
    //   offset: (paging.pageIndex - 1) * paging.pageSize,
    //   limit: paging.pageSize,
    // }

    // if (attendModels.list) {
    //   options.include = attendModels.list.map(v => {
    //     let m = {};
    //     if (v instanceof Object) {
    //       m.model = db[Object.keys(v)[0]]
    //       m.attributes = Object.values(v)[0];
    //     } else {
    //       m.model = db[v];
    //     }
    //     console.log(m)
    //     return m;
    //   })
    // }
    let options = {}
    
    if (attendModels.list) {
      options.include = attendModels.list.map(v => {
        let m = {};
        if (v instanceof Object) {
          m.model = db[Object.keys(v)[0]]
          m.attributes = Object.values(v)[0];
        } else {
          m.model = db[v];
        }
        console.log(m)
        return m;
      })
    }
    let data = await queryPaginDataAsync(db[modelName],ctx.request.query,options)
    // let data = await db[modelName].findAndCountAll(options)

    //console.log('------------')
    ctx.body = result.success(data);
  })
  /*
    get info
  */
  router.get(`${prefixPath}/${modelName.toLowerCase()}`, async (ctx, next) => {
    let id = ctx.request.query.id;
    let options = {
      where: {
        id
      }
    }
    if (attendModels.list) {
      options.include = attendModels.list.map(v => {
        let m = {};
        if (v instanceof Object) {
          m.model = db[Object.keys(v)[0]]
          m.attributes = Object.values(v)[0];
        } else {
          m.model = db[v];
        }
        console.log(m)
        return m;
      })
    }
    let data = await db[modelName].findOne(options)

    ctx.body = result.success(data);
  })
  /**
   * add/edit 
   */
  router.post(`${prefixPath}/${modelName.toLowerCase()}`, async (ctx, next) => {
    let item = {
      ...ctx.request.body
    };
    let id = item.id;
    delete item.id;
    if (id) {
      await db[modelName].update(item,{where:{id}})
      item = await db[modelName].findByPk(id); 
      /*此处在某些server中 save()出错
      let _item = await db[modelName].findByPk(id);
      Object.assign(_item, item)
      item= await _item.save();
      item = _item;
      */
    } else {
 
      item.rank = item.rank ? item.rank : new Date().getTime();
      item = await db[modelName].create(item)
    }

    //关联处理
    if (attendModels.postList) {
      attendModels.postList.forEach(async m => {
        let ms = getNameComplex(m)
        let postM = ctx.request.body[ms];
        if (postM && postM.length > 0) {
          let mItems = await db[m].findAll({
            where: {
              [Op.or]: postM.map(id => {
                return {
                  id
                }
              })

            }
          });
          await item['set' + ms](mItems);
        }
      });
    }
    ctx.body = result.success(item.id);
  })
  /**
   * del 
   */
  router.delete(`${prefixPath}/${modelName.toLowerCase()}`, async (ctx, next) => {
    let id = ctx.request.query.id;
    let error = "";

    //关联处理
    if (attendModels.delRejectList) {

      for (var i in attendModels.delRejectList) {
        let m = attendModels.delRejectList[i];
        let ms = getNameComplex(m)
        let xx = {
          where: {
            [modelName + 'Id']: id
          }
        }
        let count = await db[m].count(xx)

        if (count > 0) {
          error = `存在${m}，不能删除`;
          break;

        }
      };
    }


    if (error) {
      ctx.body = result.error(error);
    } else {
      //级联删除
      if (attendModels.delList) {

        for (var i in attendModels.delList) {
          let m = attendModels.delList[i];
          await db[m].destroy({
            where: {
              [modelName + "Id"]: id
            }
          })

        };
      }
      let item = await db[modelName].findByPk(id);
      await item.destroy()


      ctx.body = result.success();
    }

  })
}