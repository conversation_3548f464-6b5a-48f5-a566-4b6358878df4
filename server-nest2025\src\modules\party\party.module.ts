import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { PartyController } from './party.controller';
import { PartyService } from './party.service';
import Party from '../../db/model/Party';
import PartyStatic from '../../db/model/PartyStatic';
import User from '../../db/model/User';
import UserLikeLog from '../../db/model/UserLikeLog';
import PartyInterest from '../../db/model/PartyInterest';
import { IndexModule } from '../index/index.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Party, PartyStatic, User, UserLikeLog, PartyInterest]),
    IndexModule
  ],
  controllers: [PartyController],
  providers: [PartyService],
})
export class PartyModule {} 