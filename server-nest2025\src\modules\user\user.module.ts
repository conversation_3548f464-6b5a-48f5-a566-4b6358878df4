import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
// import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { IndexModule } from '../index/index.module';
import { CommonModule } from '../common/common.module';
import User from '../../db/model/User';
import Account from '../../db/model/Account';
import League from '../../db/model/League';
import UserPost from '../../db/model/UserPost';
import VenueVisitLog from '../../db/model/VenueVisitLog';
import UserGallery from '../../db/model/UserGallery';
import UserLikeLog from '../../db/model/UserLikeLog';
import Venue from '../../db/model/Venue';
import UserApplyManager from '../../db/model/UserApplyManager';
import siteConfig from '../../siteConfig';

@Module({
  imports: [
    JwtModule.registerAsync({
      // imports: [ConfigModule],
      useFactory: async () => ({
        secret: siteConfig.JWT_SECRET || 'your-secret-key',
        signOptions: { expiresIn: '30d' },
      }),
    }),
    SequelizeModule.forFeature([
      User,
      Account,
      League,
      UserPost,
      VenueVisitLog,
      UserGallery,
      UserLikeLog,
      Venue,
      UserApplyManager
    ]),
    IndexModule,
    CommonModule
  ],
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService]
})
export class UserModule {} 