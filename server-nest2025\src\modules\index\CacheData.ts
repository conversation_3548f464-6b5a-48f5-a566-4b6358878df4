export type ICacheData = {
    sports: any[];
    SportId: string;
    matchClasses: any[];
    newLeague: {
      base: any;
      teams: any[];
      matchs: any[];
    };
    sys: Record<string, any>;
    assets: any[];
    wxAccessToken: string;
    cityCards: ICityCardData[];
  }

const CacheData:ICacheData = {
    sports: [],
    SportId: '',
    matchClasses: [],
    newLeague: {
      base: {},
      teams: [],
      matchs: [],
    },
    sys: {},
    assets: [],
    wxAccessToken: '',
    
    cityCards: [
      // {
      //   province: '北京',
      //   city: '北京',
      //   divisionCode: 430100,
      //   cTeamCount: 0,
      //   memberCount: 0,
      //   venueCount: 0,
      // }
    ]
  };

  export default CacheData;