//地点，场所-正式
import {
  BelongsTo,
  Column,
  DataType,
  Default,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import User from './User';
import BaseEntity from './_BaseEntity';

@Table
export default class UserLikeLog extends Model {
  @Column(DataType.UUID)
  declare targetId: string;

  @Column
  declare type: number;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  declare UserId: string;
  @BelongsTo(() => User, { constraints: false, foreignKey: 'UserId' })
  declare User: User;
}
