import {
  BelongsTo,
  Column,
  DataType,
  Default,
  ForeignKey,
  HasMany,
  Table,
} from 'sequelize-typescript';
import  BaseUUIDEntity from './_BaseUUIDEntity';
import League from './League';
import MatchClass from './MatchClass';
import MatchTeam from './MatchTeam';
import MatchTeamPlayer from './MatchTeamPlayer';
import Sport from './Sport';
import MatchTeamResult from './MatchTeamResult';
@Table
export default class Match extends BaseUUIDEntity {
    @Column
    declare name: string

    @Column
    declare beginDate: number //时间戳

    @Column
    declare endDate: number

    @Default(0)
    @Column
    declare state:number//0:未开始,1:进行中,2:结束

    @Column
    declare intro: string

    @Column
    declare address: string

    @Column
    declare longitude: string //经度

    @Column
    declare latitude: string //维度

    @Column
    declare type: string // 单人，团体,

    @Column
    declare bak: string

    @Column
    declare attLink: string //文字数据关系 sport_league
    
    @Column(DataType.STRING(1000))
    declare extUrl: string

    @ForeignKey(() => Sport)
    @Column(DataType.UUID)
    declare SportId: number

    @ForeignKey(() => League)
    @Column(DataType.UUID)
    declare LeagueId: string
    @BelongsTo(()=>League,{constraints:false,foreignKey:"LeagueId"})
    declare League:League

    @ForeignKey(() => MatchClass)
    @Column(DataType.UUID)
    declare MatchClassId:string
    @BelongsTo(()=>MatchClass,{constraints:false,foreignKey:"MatchClassId"})
    declare MatchClass:MatchClass
    
    @HasMany(()=>MatchTeam,{constraints:false})
    declare MatchTeams:MatchTeam[]

    @HasMany(()=>MatchTeamResult,{constraints:true})
    declare MatchTeamResults:MatchTeamResult[]

    @BelongsTo(()=>Sport,{constraints:false,foreignKey:"SportId"})
    declare Sport:Sport

    @HasMany(()=>MatchTeamPlayer)
    declare MatchTeamPlayers:MatchTeamPlayer[]
    
}