module.exports = function(router,db,result){
/**
 * get item
 */
router.get('/meet/linkGroup',async(ctx,next)=>{
  let meetId = ctx.request.query.meetId;
  let speakers = await db.MeetLinkGroup.findAll({where:{MeetId:meetId}})
  ctx.body=result.success(speakers);
})
/**
 * add/edit item
 */
router.post('/meet/linkGroup',async(ctx,next)=>{
  let linkGroup = {...ctx.request.body};
  if(linkGroup.id){
    let _linkGroup = await db.MeetLinkGroup.findOne({where:{id:linkGroup.id}});
    Object.assign(_linkGroup,linkGroup)
    await timePoint.save();
  }else{
    delete linkGroup.id;
    linkGroup = await db.MeetLinkGroup.create(linkGroup)
  }
  ctx.body=result.success(linkGroup.id);
})
/**
 * del item
 */
router.delete('/meet/item',async(ctx,next)=>{
  let id = ctx.request.query.id;
  await db.MeetLinkGroup.destroy({where:{id}})
  ctx.body=result.success();
})
}
