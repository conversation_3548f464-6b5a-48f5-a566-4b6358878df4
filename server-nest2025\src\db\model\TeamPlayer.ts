import {
  Column,
  DataType,
  De<PERSON>ult,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import Team from './Team';
import Player from './Player';

@Table({timestamps: false})
export default class TeamPlayer extends Model {
  @Column(DataType.BIGINT)
  declare rank: Number
  
  @ForeignKey(()=>Team)
  @Column(DataType.UUID)
  declare TeamId:string

  @ForeignKey(()=>Player)
  @Column(DataType.UUID)
  declare PlayerId:string
}