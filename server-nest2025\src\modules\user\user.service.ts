import { Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { IndexService } from '../index/index.service';
import { newAccountToken, queryPaginDataAsync } from '../../utils/funs';
import { getOpenidAsync, decryptData, appId } from '../../utils/wxHelper';
import { AccountType, LikeType } from '../../utils/enums';
import shortid from 'shortid';
import User from '../../db/model/User';
import Account from '../../db/model/Account';
import League from '../../db/model/League';
import UserPost from '../../db/model/UserPost';
import VenueVisitLog from '../../db/model/VenueVisitLog';
import UserGallery from '../../db/model/UserGallery';
import UserLikeLog from '../../db/model/UserLikeLog';
import Venue from '../../db/model/Venue';
import UserApplyManager from '../../db/model/UserApplyManager';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApplyManagerDto } from './dto/apply-manager.dto';
import { CommonService } from '../common/common.service';
import divisionCodeMap from '../../utils/china-division.json';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(Account)
    private accountModel: typeof Account,
    @InjectModel(League)
    private leagueModel: typeof League,
    @InjectModel(UserPost)
    private userPostModel: typeof UserPost,
    @InjectModel(VenueVisitLog)
    private venueVisitLogModel: typeof VenueVisitLog,
    @InjectModel(UserGallery)
    private userGalleryModel: typeof UserGallery,
    @InjectModel(UserLikeLog)
    private userLikeLogModel: typeof UserLikeLog,
    @InjectModel(Venue)
    private venueModel: typeof Venue,
    @InjectModel(UserApplyManager)
    private userApplyManagerModel: typeof UserApplyManager,
    private indexService: IndexService,
    private commonService: CommonService,
  ) {}

  // async getToken(code: string, openid?: string) {
  //   try {
  //     if (!openid) {
  //       const result = await getOpenidAsync(code);
  //       openid = result.openid;
  //     }

  //     const user = await this.userModel.findOne({
  //       where: { openid },
  //       attributes: ['id', 'openid','level'],
  //       include: [{
  //         model: this.accountModel,
  //         attributes: ['type'],
  //         through: { attributes: [] }
  //       }]
  //     }) as User&{account?:Account};

  //     if (!user) {
  //       throw new Error('未注册');
  //     }

  //     const { token, expire } = newAccountToken({ id: user.id, openid,level:user.level,type:(user.account?.type||null) }, 2592000);
  //     return { token, expire };
  //   } catch (e) {
  //     return { token: '', expire: -1, msg: e.message };
  //   }
  // }

  async tryLoginRegister(code: string, openid?: string, ip?: string) {

    if (!openid) {
      const result = await getOpenidAsync(code);
      openid = result.openid;
    }

    let user = await this.userModel.findOne({
      where: { openid },
      attributes: ['id', 'openid','level','nickName','name','avatar','gender','city','province','country','address','phone','sign','from','divisionCode'],
      include: [{
        model: this.accountModel,
        attributes: ['type','name'],
        required: false
      }],
      raw: true,
      nest: true
    }) ;
    
    if (!user) {
      const location = await this.commonService.getLocationByIp(ip||'');
      const province = location?.province || '';
      const city = location?.city || '';
      const country = location?.country || '';  
      const name = shortid.generate();
      const divisionCode = divisionCodeMap[province]?.[city] || 0;
      user = await this.register(openid, {
        nickName: name,
        name: '',
        avatar: '',
        gender: '',
        city: city,
        province: province,
        country: country,
        address: location?.fullAddress||'',
        phone: '',
        sign: '',
        from: ip,
        divisionCode: divisionCode
      })
    }
    //console.log(user);
    const { token, expire } = newAccountToken({ id: user.id, openid,level:user.level,type:(user.Account?.type||null) }, 2592000);

    const info = {
      id: user.id,
      nickName: user.nickName,
      name: user.name,
      avatar: user.avatar,
      gender: user.gender,
      city: user.city,
      province: user.province,
      country: user.country,  
      address: user.address,
      phone: user.phone,
      sign: user.sign,
      from: user.from,
      divisionCode: user.divisionCode
    };
    const account = {
      type: user.Account?.type||null,
      name: user.Account?.name||null,
      //leagues: user.account?.Leagues||null,
    };
    return { token, expire,info,account};

  }
  // 注册用户信息
  async register(openid: string, updateUserDto: UpdateUserDto): Promise<User> {

    if (!openid) {
      throw new UnauthorizedException('获取openid失败');
    }

    let user = await this.userModel.findOne({
      where: { openid }
    });

    if (!user) {
      user = await this.userModel.create({
        openid,
        nickName: updateUserDto.nickName,
        from: updateUserDto.from,
        avatar: updateUserDto.avatar,
        gender: updateUserDto.gender,
        city: updateUserDto.city,
        province: updateUserDto.province,
        country: updateUserDto.country,
        address: updateUserDto.address,
        phone: updateUserDto.phone,
        sign: updateUserDto.sign,
        name: updateUserDto.name,
        level: 1,
        state: 1,
        divisionCode: updateUserDto.divisionCode
      });
    }

    return user;
  }

  async   updateUser(updateUserDto: UpdateUserDto, userId: string) {
    const user = await this.userModel.findByPk(userId);
    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    const allowedFields = ['name', 'phone', 'sign', 'address', 'lon', 'lat', 'avatar', 'nickName'];
    for (const field of allowedFields) {
      if (updateUserDto[field] !== undefined) {
        user[field] = updateUserDto[field];
      }
    }

    await user.save();
    return true;
  }

  async getUserAccount(userId: string) {
    const user = await this.userModel.findOne({
      where: { id: userId },
      attributes: ['id', 'nickName', 'avatar','country', 'city', 'province',  'level', 'openid', 'state', 'phone', 'sex', 'address', 'name', 'sign'],
      include: [{
        model: this.accountModel,
        attributes: ['type', 'name'],
        include: [{
          model: this.leagueModel,
          attributes: ['id'],
          through: { attributes: [] }
        }]
      }]
    });

    if (!user) {
      return null;
    }

    const userData = user.get();
    const account = userData.Account;
    delete userData.Account;

    if (account) {
      userData.accountName = account.name;
      userData.accountType = account.type;
      userData.leagues = account.Leagues;
    } else {
      userData.accountName = '';
      userData.accountType = '';
      userData.leagues = [];
    }

    return userData;
  }

  async applyManager(applyManagerDto: ApplyManagerDto) {
    const { openid, nickName, avatar, type, content, applyTarget } = applyManagerDto;

    if (!openid || !nickName || !type) {
      throw new UnauthorizedException('参数错误');
    }

    await this.userApplyManagerModel.create({
      ...applyManagerDto,
      SportId: this.indexService.getCacheData().SportId
    });

    return true;
  }

  async getUserInfo(id: string) {
    return await this.userModel.findByPk(id, {
      attributes: ['id', 'name', 'nickName', 'sex', 'sign', 'avatar']
    });
  }

  async getUserPosts(id: string, query: any) {
    return await queryPaginDataAsync(
      this.userPostModel,
      query,
      {
        where: { UserId: id },
        order: [['updatedAt', 'DESC']]
      },
      true
    );
  }

  async getUserVisits(id: string, query: any) {
    const result = await queryPaginDataAsync(
      this.venueVisitLogModel,
      query,
      {
        where: { UserId: id },
        order: [['updatedAt', 'DESC']]
      },
      true
    );

    result.list = result.list.map(v => {
      const data = v.get();
      data.venueName = data.name;
      return data;
    });

    return result;
  }

  async getUserGallery(id: string, query: any) {
    return await queryPaginDataAsync(
      this.userGalleryModel,
      query,
      {
        where: { UserId: id },
        attributes: ['id', 'url'],
        order: [['updatedAt', 'DESC']]
      },
      true
    );
  }

  async getUserVenues(id: string, query: any) {
    return await queryPaginDataAsync(
      this.userLikeLogModel,
      query,
      {
        where: {
          UserId: id,
          type: LikeType.Venue
        },
        attributes: ['id', 'targetId', 'updatedAt'],
        order: [['updatedAt', 'DESC']],
        include: [{
          model: this.venueModel,
          attributes: ['id', 'name', 'address', 'images']
        }]
      },
      true
    );
  }
} 