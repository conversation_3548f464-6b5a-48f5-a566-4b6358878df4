const {parseQueryPaging,parseQueryParam} = require("./../../utils/funs.js")
module.exports = function (router, db, result) {


  router.get('/sport/team/query', async (ctx, next) => {
    let paging = parseQueryPaging(ctx.request.query);
    let where = parseQueryParam(ctx.request.query);
    let options = {
      where: {},
      order: [
        ['createdAt', 'DESC']
      ],
      offset: (paging.pageIndex - 1) * paging.pageSize,
      limit: paging.pageSize,
    }
    if (where.LeagueId) {
      options.include = [{
        model: db.League,
        attributes:[],
        where: {
          id: where.LeagueId,
        }
      }]
      delete where.LeagueId;
    }
    options.where = where;

    let data = await db.Team.findAndCountAll(options)

    ctx.body = result.success({
      list: data.rows,
      total: data.count,
      pageSize: paging.pageSize,
      pageIndex: paging.pageIndex
    });
  });

}