//场所-统计数据表
import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Table,
} from 'sequelize-typescript';
import BaseEntity from './_BaseEntity';
import User from './User';

@Table
export default class UserGallery extends BaseEntity {
  @Column
  declare url: string;

  @Column
  declare type: string; //img,video

  @Column
  declare name: string;

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  declare UserId: string
  @BelongsTo(() => User, { constraints: false, foreignKey: 'UserId' })
  declare User: User;
}
