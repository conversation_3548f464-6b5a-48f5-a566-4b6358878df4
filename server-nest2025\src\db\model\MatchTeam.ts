import { <PERSON>ongsTo, Column, DataType, <PERSON>fault, Foreign<PERSON>ey, HasMany, Table } from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import Match from './Match';
import Team from './Team';
import MatchTeamPlayer from './MatchTeamPlayer';

@Table
export default class MatchTeam extends BaseUUIDEntity {
  @Default(0)
  @Column
  declare state: number; //0:未开始,1:进行中,2:结束

  @Default(0)
  @Column(DataType.DECIMAL)
  declare score: number;

  @Column
  declare scoreDetails: string;

  @Column
  declare bak: string;

  @Column(DataType.STRING(1000))
  declare content: string;
  
  @Column
  declare attLink: string; //文字数据关系 sport_league_match

  @ForeignKey(()=>Match)
  @Column(DataType.UUID)
  declare MatchId:string
  @BelongsTo(()=>Match,{constraints:false,foreignKey:"MatchId"})
  declare Match:Match

  @ForeignKey(()=>Team)
  @Column(DataType.UUID)
  declare TeamId:string
  @BelongsTo(()=>Team,{constraints:false,foreignKey:"TeamId"})
  declare Team:Team
  
  @HasMany(()=>MatchTeamPlayer,{constraints:false,foreignKey:"MatchTeamPlayerId"})
  declare Players:MatchTeamPlayer[]
}
