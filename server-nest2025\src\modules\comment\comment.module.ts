import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CommentController } from './comment.controller';
import { CommentService } from './comment.service';
import UserComment from '../../db/model/UserComment';
import User from '../../db/model/User';
import { IndexModule } from '../index/index.module';

@Module({
  imports: [
    SequelizeModule.forFeature([UserComment, User]),
    IndexModule
  ],
  controllers: [CommentController],
  providers: [CommentService],
  exports: [CommentService]
})
export class CommentModule {}