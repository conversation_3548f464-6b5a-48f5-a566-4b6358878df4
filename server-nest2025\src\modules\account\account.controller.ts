import { Controller, Get, Post, Delete, Body, Query, Req, UseGuards } from '@nestjs/common';
import { AccountService } from './account.service';
import { AccountDto } from './dto/account.dto';
import config from 'src/siteConfig';

@Controller(`${config.API_PREFIX}/accounts`)
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @Get()
  async getAccounts(@Query() query: any) {
    return this.accountService.getAccounts(query);
  }

  @Post()
  async createOrUpdateAccount(@Body() dto: AccountDto, @Req() req: any) {
    // 假设 req.user 是当前登录用户
    return this.accountService.createOrUpdateAccount(dto, req.user);
  }

  @Post('unBind')
  async unBindAccount(@Body('id') id: string) {
    return this.accountService.unBindAccount(id);
  }

  @Delete()
  async deleteAccount(@Query('id') id: string, @Req() req: any) {
    return this.accountService.deleteAccount(id, req.user);
  }
}