import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { MatchService } from './match.service';
import { UpdateMatchScoreDto } from './dto/update-match-score.dto';
import { QueryMatchDto } from './dto/query-match.dto';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { AccountType } from '../../utils/enums';
import { RequestUser } from '../../auth/decorators/requestUser.decorator';
import config from 'src/siteConfig';

@ApiTags('比赛')
@Controller(`${config.API_PREFIX}/match`)
export class MatchController {
  constructor(private readonly matchService: MatchService) {}

  @ApiOperation({ summary: '更新比赛成绩' })
  @UseGuards(RoleGuard)
  @Roles(AccountType.Operator,AccountType.Admin)
  @Post('updateMatchResultScore')
  async updateMatchResultScore(
    @Body() updateDto: UpdateMatchScoreDto,
    @RequestUser() user: ITokenUser
  ) {
    return await this.matchService.updateMatchResultScore(updateDto, user);
  }

  @ApiOperation({ summary: '获取比赛列表' })
  @Get('matchs')
  async getMatches(@Query() query: QueryMatchDto) {
    return await this.matchService.getMatches(query);
  }

  @ApiOperation({ summary: '获取比赛详情' })
  @ApiParam({ name: 'id', description: '比赛ID' })
  @Get(':id')
  async getMatch(@Param('id') id: string) {
    return await this.matchService.getMatch(id);
  }
}