import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AccountType, UserLevel } from '../../utils/enums';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredTypes = this.reflector.getAllAndOverride<AccountType[]>('accountTypes', [
      context.getHandler(),
      context.getClass(),
    ]);

    const requiredLevels = this.reflector.getAllAndOverride<UserLevel[]>('userLevels', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredTypes && !requiredLevels) {
      return true;
    }

    const { user } = context.switchToHttp().getRequest();
    if (!user) {
      throw new ForbiddenException('未登录');
    }

    // 检查用户
    if ((requiredLevels && !requiredLevels.includes(user.level)) && (requiredTypes && !requiredTypes.includes(user.type))) {
      throw new ForbiddenException('权限不足');
    }



    return true;
  }
}