//地点，场所-正式
import {
  BelongsToMany,
  Column,
  DataType,
  Default,
  HasMany,
  HasOne,
  Table,
} from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import VenueStatic from './VenueStatic';
import UserPost from './UserPost';
import VenueVisitLog from './VenueVisitLog';

@Table
export default class Venue extends BaseUUIDEntity {
  @Column
  declare name: string;

  @Column
  declare address: string;

  @Column
  declare lat: string; //地址坐标

  @Column
  declare lon: string; //地址坐标

  @Column
  declare city: string; //市

  @Column
  declare province: string; //省

  @Column
  declare district: string; //区

  @Column
  declare divisionCode: number; //区划代码

  @Default(0)
  @Column
  declare type: number; //建筑类型 0:, 1:室外,2室内

  @Default(0)
  @Column
  declare able: number; //全天，白天，夜晚,已废弃

  @Default(0)
  @Column
  declare size: number; //场地大小，大型多场地，中型多场地，小型，

  @Default(0)
  @Column
  declare paid: number; //收费情况 1:免费 2:收费 3:混合

  @Default(0)
  @Column
  declare parking: number; //停车位 1:无 2:较少 3:充裕,

  @Column
  declare paidIntro: string; //收费说明

  @Column
  declare intro: string; //简短介绍,

  @Column
  declare contacter: string; //联系人

  @Column
  declare contacterPhone: string; //电话,

  @Column
  declare tag: string;

  @Column(DataType.STRING(2000))
  declare images: string;

  @Column
  declare star: number; //推荐星级 0-5级

  @Default(0)
  @Column
  declare state: number; //状态 0:未审批,1:验证过,2:审批通过,3,验证+审批

  @Column
  declare createUserId: string; //创建者

  @Column
  declare updateUserId: string; //更新者

  @Column(DataType.UUID)
  declare SportId: string

  @HasOne(() => VenueStatic, { foreignKey: 'id', sourceKey: 'id' })
  declare Static: VenueStatic

  @HasMany(() => UserPost, { constraints: false })
  declare Posts: UserPost[]

  @HasMany(() => VenueVisitLog, { constraints:false })
  declare VisitLogs: VenueVisitLog[]
}
