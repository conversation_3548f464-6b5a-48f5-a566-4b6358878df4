import {
  Column,
  <PERSON>,
  PrimaryK<PERSON>,
  Default,
  IsUUID,
  Table,
  DataType
} from 'sequelize-typescript';

export default class _BaseUUIDEntity extends Model {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true
  })
  declare id: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW
  })
  declare createdAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    defaultValue: DataType.NOW
  })
  declare updatedAt: Date;

  @Default(0)
  @Column(DataType.BIGINT)
  declare rank: number

  @Default(0)
  @Column
  declare disabled: number //0:可用
}