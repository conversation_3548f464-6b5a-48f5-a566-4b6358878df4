//无关联类库
import CryptoJS from 'crypto-js';

  //MD5 加密
export function cryptPwd(password) {
    return CryptoJS.MD5(password + "666n").toString();
  }
  export function merge (target, ...arg) {
    function isObject (obj) {
      return Object.prototype.toString.call(obj) === '[object Object]'
    }
    function isArray (arr) {
      return Array.isArray(arr)
    }
      return arg.reduce((acc, cur) => {
        return Object.keys(cur).reduce((subAcc, key) => {
          const srcVal = cur[key]
          if (isObject(srcVal)) {
            subAcc[key] = merge(subAcc[key] ? subAcc[key] : {}, srcVal)
          } else if (isArray(srcVal)) {
            // series: []，下层数组直接赋值
            subAcc[key] = srcVal.map((item, idx) => {
              if (isObject(item)) {
                const curAccVal = subAcc[key] ? subAcc[key] : []
                return merge(curAccVal[idx] ? curAccVal[idx] : {}, item)
              } else {
                return item
              }
            })
          } else {
            subAcc[key] = srcVal
          }
          return subAcc
        }, acc)
      }, target)

  }