# API 响应格式统一说明

## 概述

项目已实现统一的API响应格式，所有接口都会自动返回标准化的JSON格式。

## 标准响应格式

```json
{
  "code": 0,        // 状态码：0表示成功，负数表示错误
  "msg": "成功",     // 消息描述
  "data": {}        // 响应数据
}
```

## 状态码说明

- `0`: 成功
- `-100`: 一般错误
- `-101`: 未登录
- `-102`: token过期
- `-103`: 无权限
- `-105`: 参数错误

## 使用方法

### 1. 正常返回数据

控制器方法可以直接返回数据，拦截器会自动格式化为标准格式：

```typescript
@Get('users')
async getUsers() {
  const users = await this.userService.findAll();
  return users; // 自动格式化为 { code: 0, msg: "成功", data: users }
}

@Get('count')
async getCount() {
  const count = await this.userService.count();
  return count; // 自动格式化为 { code: 0, msg: "成功", data: count }
}

@Post('create')
async createUser(@Body() dto: CreateUserDto) {
  const result = await this.userService.create(dto);
  return result; // 自动格式化为 { code: 0, msg: "成功", data: result }
}
```

### 2. 返回布尔值

```typescript
@Post('delete/:id')
async deleteUser(@Param('id') id: string) {
  const success = await this.userService.delete(id);
  return success; // true -> { code: 0, msg: "成功", data: true }
                  // false -> { code: -100, msg: "失败", data: false }
}
```

### 3. 抛出异常

使用 NestJS 内置的异常类：

```typescript
import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';

@Get('user/:id')
async getUser(@Param('id') id: string) {
  const user = await this.userService.findById(id);
  if (!user) {
    throw new NotFoundException('用户不存在'); // 自动格式化为 { code: -100, msg: "用户不存在", data: null }
  }
  return user;
}

@Post('update')
async updateUser(@Body() dto: UpdateUserDto) {
  if (!dto.name) {
    throw new BadRequestException('用户名不能为空'); // 自动格式化为 { code: -100, msg: "用户名不能为空", data: null }
  }
  return await this.userService.update(dto);
}
```

### 4. 跳过格式化

如果某个接口不需要自动格式化，可以使用 `@SkipResponseFormat()` 装饰器：

```typescript
import { SkipResponseFormat } from '../../utils/skip-response-format.decorator';

@Get('raw-data')
@SkipResponseFormat()
async getRawData() {
  return { custom: 'format' }; // 直接返回，不进行格式化
}
```

### 5. 使用原有的 jsonResult（可选）

如果仍然想使用原有的 `jsonResult` 工具，也是可以的：

```typescript
import { jsonResult } from '../../utils/jsonResult';

@Get('custom')
async getCustomResponse() {
  const data = await this.service.getData();
  return jsonResult.success(data); // 已经是标准格式，拦截器不会重复处理
}

@Post('error')
async handleError() {
  return jsonResult.error('自定义错误信息', -105);
}
```

## 示例响应

### 成功响应

```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "id": "123",
    "name": "张三",
    "email": "<EMAIL>"
  }
}
```

### 列表响应

```json
{
  "code": 0,
  "msg": "成功",
  "data": {
    "list": [
      {"id": "1", "name": "用户1"},
      {"id": "2", "name": "用户2"}
    ],
    "total": 2,
    "pageSize": 10,
    "pageIndex": 1
  }
}
```

### 错误响应

```json
{
  "code": -100,
  "msg": "用户不存在",
  "data": null
}
```

## 注意事项

1. 所有控制器方法都会自动应用响应格式化
2. 异常会自动被捕获并格式化为标准错误格式
3. 如果返回的数据已经是标准格式（包含 code、msg、data 字段），则不会重复处理
4. 使用 `@SkipResponseFormat()` 装饰器可以跳过特定接口的格式化
5. 原有的 `jsonResult` 工具仍然可以使用，不会产生冲突 