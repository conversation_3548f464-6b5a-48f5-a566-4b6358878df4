/*
100:出错,
101
*/
let myError = require('./myError')

const ErrorType = {

  出错: -100,
  未登录: -101,
  token出期: -102,
  无权限: -103,
  参数错误: -105,

}

export const jsonResult = {
  success(data) {
    return {
      code: 0,
      msg: '成功',
      data
    }
  },
  error(msg = "出错", code = -100) {
    return {
      code,
      msg: msg,
      data: null
    }
  },
  exception(msg,code = -100){
    throw new myError(msg,code)
  }
}