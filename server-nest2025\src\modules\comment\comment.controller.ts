import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { CommentService } from './comment.service';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { AccountType, UserLevel } from '../../utils/enums';
import { CreateCommentDto } from './dto/create-comment.dto';
import { RequestUser } from '../../auth/decorators/requestUser.decorator';
import config from 'src/siteConfig';

@ApiTags('评论')
@Controller(`${config.API_PREFIX}/comment`)
export class CommentController {
  constructor(private readonly commentService: CommentService) {}

  @ApiOperation({ summary: '获取评论列表' })
  @ApiParam({ name: 'targetId', description: '目标ID' })
  @Get('list')
  async getComments(@Query() query: any) {
    return await this.commentService.getComments(query.targetId, query);
  }

  @ApiOperation({ summary: '提交评论' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member,UserLevel.Vip,UserLevel.Admin)
  @Post()
  async createComment(@Body() createCommentDto: CreateCommentDto, @RequestUser() user: any) {
    return await this.commentService.createComment(createCommentDto, user.id);
  }

  @ApiOperation({ summary: '涂抹评论内容' })
  @UseGuards(RoleGuard)
  @Roles(AccountType.Operator, AccountType.Admin)
  @Post('cover')
  async coverComment(@Body('commentId') commentId: string) {
    return await this.commentService.coverComment(commentId);
  }

  @ApiOperation({ summary: '屏蔽评论' })
  @UseGuards(RoleGuard)
  @Roles(AccountType.Operator, AccountType.Admin)
  @Post('disable')
  async disableComment(@Body('commentId') commentId: string) {
    return await this.commentService.disableComment(commentId);
  }
}