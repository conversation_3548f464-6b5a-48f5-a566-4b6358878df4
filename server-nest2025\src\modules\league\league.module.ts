import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { LeagueController } from './league.controller';
import { LeagueService } from './league.service';
import League from '../../db/model/League';
import MatchClass from '../../db/model/MatchClass';
import { IndexModule } from '../index/index.module';

@Module({
  imports: [
    SequelizeModule.forFeature([League, MatchClass]),
    IndexModule
  ],
  controllers: [LeagueController],
  providers: [LeagueService],
  exports: [LeagueService]
})
export class LeagueModule {}