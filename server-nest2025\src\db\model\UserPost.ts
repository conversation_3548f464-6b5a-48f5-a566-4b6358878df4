import { BelongsTo, BelongsToMany, Column, DataType, Default, ForeignKey, HasOne, Model, Table } from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import User from './User';
import Venue from './Venue';
@Table
export default class UserPost extends BaseUUIDEntity {
  @Column
  declare type: string; //类型

  @Column
  declare name: string

  @Column(DataType.STRING(1000))
  declare content: string

  @Column(DataType.STRING(1000))
  declare images: string

  @Column
  declare tags: string;

  @Default(0)
  @Column
  declare access: number; //0:所有人可见，1仅好友，2不可见


  @ForeignKey(() => Venue)
  @Column(DataType.UUID)
  declare VenueId?: string; 

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  declare UserId: string


  @BelongsTo(() => User, { constraints: false, foreignKey: 'UserId' })
  declare User: User;

  @BelongsTo(() => Venue, { constraints: false, foreignKey: 'VenueId' })
  declare Venue: Venue;

}
