import {
    BelongsTo,
    BelongsToMany,
    Column,
    DataType,
    Default,
    ForeignKey,
    Table,
  } from 'sequelize-typescript';
import Sport from './Sport';
import User from './User';
import BaseEntity from './_BaseEntity';
  
@Table
  export default class CityTeam extends BaseEntity {
      @Column
      declare name: string
  
      @Column
      declare intro: string
  
      @Column
      declare avatar: string
  
      @Column(DataType.STRING(2000))
      declare content: string
  
      @Column
      declare bak: string

      /* 二进制CityTeamType */
      @Column
      declare type: number

      /* 球队人数 */
      @Column
      declare size: number

      @Default("")
      @Column
      declare country: string
      
      @Default("")
      @Column
      declare province: string
  
      @Default("")
      @Column
      declare city: string
  
      @Default("")
      @Column
      declare district: string


      @Column(DataType.INTEGER)
      declare divisionCode: number
  
      @Default("")
      @Column
      declare address: string
  
      @Column
      declare leader: string
  
      @Column
      declare leaderLink: string
  
      @Column(DataType.STRING(2000))
      declare images: string
  
      @ForeignKey(() => Sport)
      @Column(DataType.UUID)
      declare SportId: string;
  
      @BelongsTo(() => Sport, {  constraints: false })
      declare Sport: Sport;

      @ForeignKey(() => User)
      @Column(DataType.UUID)
      declare CreatedUserId: string;

      @BelongsTo(() => User, {  constraints: false })
      declare CreatedUser: User;
  
      // @BelongsToMany(() => Player, () => TeamPlayer)
      // Players: Player[];
  }