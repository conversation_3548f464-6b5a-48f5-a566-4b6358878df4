import { Controller, Get, Post, Body, Param, Query, UseGuards, BadRequestException } from '@nestjs/common';
import { IndexService } from './index.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import Sys from '../../db/model/Sys';
import { InjectModel } from '@nestjs/sequelize';
import * as enums from '../../utils/enums';
import { AccountType } from '../../utils/enums';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { RoleGuard } from 'src/auth/guards/role.guard';
import config from 'src/siteConfig';
import chinaDivision from 'src/utils/china-division.json';

const divisonCityMap = chinaDivision.data.map(v => {
  return {
    name:v.ns || v.n,
    code: v.v,
    citys:v.c.map(v=>{
      return {
        name: v.ns || v.n,
        code: v.v,
      }
    }),
  }
})

const enumsMap = Object.entries(enums).reduce((acc, [key, value]) => {
  acc[key] = Object.entries(value).reduce((subAcc, [k, v]) => {
    if(typeof v === 'number') {
      subAcc[v] = k;
    }
    return subAcc;
  }, {});
return acc;
}, {})

@ApiTags('缓存管理')
@Controller(`${config.API_PREFIX}`)
export class IndexController {
  constructor(
    private readonly indexService: IndexService,
    @InjectModel(Sys)
    private readonly sysModel: typeof Sys,
  ) {}

  @Get('base')
  @ApiOperation({ summary: '获取基础数据' })
  @ApiResponse({ status: 200, description: '成功获取基础数据' })
  async getBaseData() {
    const cacheData = this.indexService.getCacheData();
    return {
      assets: cacheData.assets,
      sys: cacheData.sys,
      enums: enumsMap
    };
  }
  @Get('base/divisonCityMap')
  @ApiOperation({ summary: '获取城市编码表' })
  @ApiResponse({ status: 200, description: '成功获取城市编码表' })
  async getBaseDivisonCity() {
    return divisonCityMap;
  }

  @Get('cache/refresh')
  @ApiOperation({ summary: '刷新所有缓存' })
  async refreshAll() {
    await this.indexService.updateAll();
    return 'ok';
  }

  @Get('cache/refresh/asset')
  @ApiOperation({ summary: '刷新资产缓存' })
  async refreshAsset() {
    await this.indexService.updateAsset();
    return 'ok';
  }

  @Get('cache/refresh/sys')
  @ApiOperation({ summary: '刷新系统设置缓存' })
  async refreshSys() {
    await this.indexService.updateSys();
    return 'ok';
  }

  @Get('sys/items')
  @ApiOperation({ summary: '获取系统设置项' })
  async getSysItems() {
    return await this.sysModel.findAll();
  }

  @Post('sys/item')
  @UseGuards(RoleGuard)
  @Roles(AccountType.Operator, AccountType.Admin)
  @ApiOperation({ summary: '更新系统设置项' })
  async updateSysItem(@Body() body: { key: string; value: string }) {
    const item = await this.sysModel.findByPk(body.key);
    if (!item) {
      throw new BadRequestException('设置项不存在');
    }
    item.value = body.value;
    await item.save();
    await this.indexService.updateSys();
    return 'ok';
  }

  @Get('newleague/refresh')
  @ApiOperation({ summary: '刷新最新赛事缓存' })
  async refreshNewLeague() {
    await this.indexService.updateNewLeagueAll();
    return 'ok';
  }

  @Get('newleague/match/result')
  @ApiOperation({ summary: '更新比赛结果' })
  async updateMatchResult(@Query('resultId') resultId: string) {
    const cacheData = this.indexService.getCacheData();
    if (cacheData.newLeague.base) {
      await this.indexService.updateNewLeagueMatchResult(resultId);
    }
    return 'ok';
  }

  @Get('newleague')
  @ApiOperation({ summary: '获取最新赛事信息' })
  async getNewLeague() {
    const cacheData = this.indexService.getCacheData();
    return {
      base: cacheData.newLeague.base,
      teams: cacheData.newLeague.teams
    };
  }

  @Get('newleague/matchs')
  @ApiOperation({ summary: '获取最新赛事的所有比赛' })
  async getNewLeagueMatches() {
    const cacheData = this.indexService.getCacheData();
    return cacheData.newLeague.matchs;
  }

  @Get('newleague/match/:id')
  @ApiOperation({ summary: '获取指定比赛详情' })
  async getNewLeagueMatch(@Param('id') id: string) {
    const cacheData = this.indexService.getCacheData();
    const match = cacheData.newLeague.matchs?.find(v => v.id === id);
    if (!match) {
      throw new BadRequestException('比赛不存在');
    }
    return match;
  }

  @Get('city/card')
  @ApiOperation({ summary: '获取城市卡片数据' })
  async getCityCard(@Query('province') province: string, @Query('city') city: string, @Query('divisionCode') divisionCode: number) {
    //console.log(province, city, divisionCode);
    if(!(province && city) && !divisionCode) {
      throw new BadRequestException('province, city, divisionCode is required');
    }
    return await this.indexService.getCityCardData(province, city, divisionCode);
  }
  @Post('city/card')
  @ApiOperation({ summary: '更新城市卡片数据' })
  async updateCityCard(@Body() body: {divisionCode: number, province: string, city: string }) {
    if(!(body.province && body.city) && !body.divisionCode) {
      throw new BadRequestException('province, city, divisionCode is required');
    }
    await this.indexService.updateCityCardData(body.province, body.city, body.divisionCode);
    return 'ok';
  }
}