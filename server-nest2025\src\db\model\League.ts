import {
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  Table,
  HasMany,
  BelongsToMany,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import Match from './Match';
import LeagueTeam from './LeagueTeam';
import MatchClass from './MatchClass';
import LeagueTeamResult from './LeagueTeamResult';
import MatchTeamResult from './MatchTeamResult';
import Account from './Account';
import LeagueAccount from './LeagueAccount';
import Sport from './Sport';
import LeagueMatchClass from './LeagueMatchClass';

@Table
export default class League extends BaseUUIDEntity{
  @Column
  declare name: string;

  @Column
  declare intro: string; //简短介绍

  @Column
  declare avatar: string;

  @Column(DataType.TEXT)
  declare content: string; //json imgs

  @Column
  declare beginDate: number;

  @Column
  declare endDate: number;

  @Column
  declare matchTypes: string; //array json 比赛模式: 单人:1，单人对抗:2，团体:3，团体对抗:4,

  @Default(0)
  @Column
  declare matchCount: number;

  @Default(0)
  @Column
  declare matchOverCount: number; //0:未开始,1:进行中,2:结束


  @Column
  declare country: string;
  
  @Column
  declare province: string;

  @Column
  declare city: string;

  @Column
  declare divisionCode: number;

  @Column
  declare address: string;

  @Column
  declare level: number; //无级，缺省,1省级赛，2市级赛，3县/区级赛，4全国赛

  @Column
  declare bak: string;

  @Default(0)
  @Column
  declare state: number; //0:未开始,1:进行中,2:结束

  @Column(DataType.STRING(1000))
  declare extUrl: string;

  @Column(DataType.STRING(2000))
  declare images: string;

  @ForeignKey(() => Sport)
  @Column(DataType.UUID)
  declare SportId: string;

  @HasMany(() => Match, { constraints: false, foreignKey: 'LeagueId' })
  declare Matchs: Match[];

  @HasMany(() => LeagueTeam, {constraints: false})
  declare LeagueTeams: LeagueTeam[];

  @HasMany(() => LeagueTeamResult)
  declare LeagueTeamResults: LeagueTeamResult[]

  @HasMany(() => MatchTeamResult)
  declare MatchTeamResults: MatchTeamResult[]

  @BelongsTo(() => Sport, { foreignKey: 'SportId' })
  declare Sport: Sport

  // @BelongsToMany(() => Account, () => LeagueAccount)
  // Accounts: Account[]

  @BelongsToMany(() => MatchClass, () => LeagueMatchClass)
  declare MatchClasses: MatchClass[];
}
