import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';

export class ApplyManagerDto {
  @ApiProperty({ description: '微信openid' })
  @IsString()
  @IsNotEmpty()
  openid: string;

  @ApiProperty({ description: '昵称' })
  @IsString()
  @IsNotEmpty()
  nickName: string;

  @ApiProperty({ description: '头像' })
  @IsString()
  @IsNotEmpty()
  avatar: string;

  @ApiProperty({ description: '申请类型' })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({ description: '申请内容' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ description: '申请目标' })
  @IsString()
  @IsNotEmpty()
  applyTarget: string;
} 