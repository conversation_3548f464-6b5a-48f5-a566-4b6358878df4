const koaBody = require('koa-body');
let path = require('path');
let fs = require('fs');
const db = require('./../../db/db.js');
const result = require('./../../utils/jsonResult');
const myError = require('./../../utils/myError');
const {
  newAccountToken
} = require("../../utils/funs")
const {cryptPwd} = require("../../utils/lib")
//token
const jwt = require('jsonwebtoken');

const routPath = "/api/manage";
const router = require('koa-router')({
  prefix: routPath
})



//token验证
router.use(async (ctx, next) => {
  //白名单
  if (['/login', '/logout',
    //'/meet/joiner/export'
  ].find(v => v == ctx.path.replace(routPath, ''))) {
    return next()
  } else {
    let token = ctx.request.header['token'] || ctx.request.query['token']
    if (token) {

      let decoded = jwt.decode(token, 'windbell2');

      if (token && decoded.exp > new Date() / 1000) {
        //如果权限没问题，那么交个下一个控制器处理
        const account = await db.Account.findByPk(decoded.accountId)
        ctx.params.account = account;
        return next();
      } else {

        throw new myError('token过期', 102);
      }
    } else {
      throw new myError('请先登录', 101)
    }
  }
})

router.get('/db', async (ctx, next) => {

  let params = ctx.request.body;
  ctx.body = {
    code: -1,
    msg: '无数据库操作method参数',
    //meets: (await db.Meet.findAll())
  }
  next()
})
/**
 * 登录
 */
router.post('/login', async (ctx, next) => {
  let {
    name = "", password = ""
  } = ctx.request.body;
  let account = await db.Account.findOne({
    where: {
      name,
      password: cryptPwd(password)
    }
  });

  if (account) {
    let accountSession = {
      token: newAccountToken({accountId:account.id}).token,
      name: account.name,
      nick: account.nick,
      type: account.type
    };
    ctx.body = result.success(accountSession);
    next()
  } else {
    throw new Error('用户或密码错误', 103)
  }
})

/**
 * 登出
 */
router.get('/logout', async (ctx, next) => {
  ctx.body = result.success()
  next()
})
/**
 * 文件上传
 */
router.post('/uploadfiles', koaBody({
  multipart: true, // 支持文件上传
  encoding: 'gzip',
  formidable: {
    //uploadDir:path.join(__dirname,'public/upload/'), // 设置文件上传目录
    //keepExtensions: true,    // 保持文件的后缀
    maxFieldsSize: 2 * 1024 * 1024, // 文件上传大小
    // onFileBegin:(name,file) => { // 文件上传前的设置
    //   // console.log(`name: ${name}`);
    //   // console.log(file);
    // },
  }
}), async (ctx, next) => {
  return new Promise(function (resolve, reject) {
    try {
      // 上传单个文件
      const file = ctx.request.files.file; // 获取上传文件
      // 创建可读流
      const reader = fs.createReadStream(file.path);
      let filePath = `/upload/${new Date().getTime()}${path.extname(file.name)}`;
      let localPath = path.join(process.cwd() + '/public');
      if (!fs.existsSync(localPath + "/upload")) {
        fs.mkdirSync(localPath + "/upload");
      }
      // 创建可写流
      const upStream = fs.createWriteStream(path.join(localPath, filePath));
      // 可读流通过管道写入可写流
      return reader.pipe(upStream).on('close', function () {
        ctx.body = result.success(filePath);
        resolve(next())
      });
    } catch (e) {
      reject(e);
    }
  });



})
var customerModelRouterFactory = require('./_publicModelRouter');
require('./venue')(router, db, result);
require('./sport')(router, db, result);
// require('./speaker')(router,db,result);
// require('./timePoint')(router,db,result);
require('./account')(router, db, result);
customerModelRouterFactory(router, db, result, 'Asset', "/sport");

customerModelRouterFactory(router, db, result, 'League', "/sport", {
  list: [{
    'MatchClass': ['id', 'name', 'avatar']
  }],
  postList: ['MatchClass', 'Team'],
  delRejectList: ['Match']
});
require('./league_plus')(router, db, result);
require('./leagueTeamResult_plus')(router, db, result);

customerModelRouterFactory(router, db, result, 'MatchClass', "/sport");

customerModelRouterFactory(router, db, result, 'MatchTeamResult', "/sport", {
  list: ['Team'],
});

customerModelRouterFactory(router, db, result, 'Document', "/sport");

customerModelRouterFactory(router, db, result, 'Match', "/sport", {
  list: [{
    'MatchTeamResult': ['id', 'score', 'state', 'rank', 'content']
  }],
  // delRejectList: ['MatchTeamResult'],
  delList: ['MatchTeamResult', 'MatchTeamPlayer']
});

customerModelRouterFactory(router, db, result, 'Team', "/sport", {});
require('./userApplyManager')(router, db, result, 'UserApplyManager', "/user");

require('./team_plus')(router, db, result);
require('./match_plus')(router, db, result);

module.exports = router;