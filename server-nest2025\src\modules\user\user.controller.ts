import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { UserService } from './user.service';
import { AuthGuard } from '../../auth/guards/auth.guard';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserLevel } from '../../utils/enums';
import { RequestUser } from '../../auth/decorators/requestUser.decorator';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApplyManagerDto } from './dto/apply-manager.dto';
import config from 'src/siteConfig';

@ApiTags('用户')
@Controller(`${config.API_PREFIX}/user`)
export class UserController {
  constructor(private readonly userService: UserService) {}

  @ApiOperation({ summary: '尝试登录注册' }) 
  @ApiQuery({ name: 'code', description: '微信登录code' })
  @Post('tryLogin')
  async tryLoginRegister(@Body() body: any, @RequestUser() user: IRequestUser) {
    //console.log(body);
    return await this.userService.tryLoginRegister(body.code, user.openid, user.ip);
  }


  @ApiOperation({ summary: '更新用户信息' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Default)
  @Post('updateUser')
  async updateUser(@Body() updateUserDto: UpdateUserDto, @RequestUser() user: IRequestUser) {
    return await this.userService.updateUser(updateUserDto, user.id);
  }

  @ApiOperation({ summary: '获取用户账户信息' })
  @Get('userAcount')
  async getUserAccount(@RequestUser() user: IRequestUser) {
    return await this.userService.getUserAccount(user.id);
  }

  @ApiOperation({ summary: '申请管理员' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  @Post('applyManager')
  async applyManager(@Body() applyManagerDto: ApplyManagerDto) {
    return await this.userService.applyManager(applyManagerDto);
  }

  @ApiOperation({ summary: '获取用户信息' })

  @Get(':id/info')
  async getUserInfo(@Param('id') id: string) {
    return await this.userService.getUserInfo(id);
  }

  @ApiOperation({ summary: '获取用户帖子列表' })

  @Get(':id/posts')
  async getUserPosts(@Param('id') id: string, @Query() query: any) {
    return await this.userService.getUserPosts(id, query);
  }

  @ApiOperation({ summary: '获取用户访问记录' })

  @Get(':id/visits')
  async getUserVisits(@Param('id') id: string, @Query() query: any) {
    return await this.userService.getUserVisits(id, query);
  }

  @ApiOperation({ summary: '获取用户相册' })

  @Get(':id/gallery')
  async getUserGallery(@Param('id') id: string, @Query() query: any) {
    return await this.userService.getUserGallery(id, query);
  }

  @ApiOperation({ summary: '获取用户收藏的场馆' })

  @Get('my/:id/venues')
  async getUserVenues(@Param('id') id: string, @Query() query: any) {
    return await this.userService.getUserVenues(id, query);
  }
} 