import { Injectable, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import Match from '../../db/model/Match';
import MatchTeamResult from '../../db/model/MatchTeamResult';
import Team from '../../db/model/Team';
import League from '../../db/model/League';
import MatchClass from '../../db/model/MatchClass';
import Account from '../../db/model/Account';
import { UpdateMatchScoreDto } from './dto/update-match-score.dto';
import { QueryMatchDto } from './dto/query-match.dto';

@Injectable()
export class MatchService {
  constructor(
    @InjectModel(Match)
    private matchModel: typeof Match,
    @InjectModel(MatchTeamResult)
    private matchTeamResultModel: typeof MatchTeamResult,
    @InjectModel(Team)
    private teamModel: typeof Team,
    @InjectModel(League)
    private leagueModel: typeof League,
    @InjectModel(MatchClass)
    private matchClassModel: typeof MatchClass,
    @InjectModel(Account)
    private accountModel: typeof Account,
  ) {}

  async updateMatchResultScore(updateDto: UpdateMatchScoreDto, user: ITokenUser) {
    const { resultId, score } = updateDto;

    if (!resultId || score === undefined) {
      throw new BadRequestException('参数错误');
    }

    const item = await this.matchTeamResultModel.findByPk(resultId);
    if (!item) {
      throw new BadRequestException('比赛结果不存在');
    }

    const account = await this.accountModel.findOne({
      where: { openid: user.openid }
    });

    if (!account || account.type < 900) {
      throw new ForbiddenException('权限错误');
    }

    await this.matchTeamResultModel.update(
      { score },
      { where: { id: item.id } }
    );

    return true;
  }

  async getMatches(query: QueryMatchDto) {
    const { LeagueId, MatchClassId, type, state } = query;
    const where: any = {};

    if (LeagueId) where.LeagueId = LeagueId;
    if (MatchClassId) where.MatchClassId = MatchClassId;
    if (type) where.type = type;
    if (state) where.state = state;

    const matches = await this.matchModel.findAll({
      where,
      include: [{
        model: this.matchTeamResultModel,
        attributes: ['id', 'rank', 'score', 'content'],
        include: [{
          model: this.teamModel,
          attributes: ['id', 'name', 'avatar']
        }]
      }]
    });

    return matches;
  }

  async getMatch(id: string) {
    const match = await this.matchModel.findOne({
      where: { id },
      include: [
        {
          model: this.leagueModel,
          attributes: ['id', 'name', 'avatar']
        },
        {
          model: this.matchClassModel,
          attributes: ['name']
        },
        {
          model: this.matchTeamResultModel,
          attributes: ['id', 'rank', 'score', 'content'],
          include: [{
            model: this.teamModel,
            attributes: ['id', 'name', 'avatar']
          }]
        }
      ]
    });

    return match;
  }
} 