import { Controller, Post, Get, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery } from '@nestjs/swagger';
import { MyService } from './my.service';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserLevel } from '../../utils/enums';
import { RequestUser } from '../../auth/decorators/requestUser.decorator';
import { CreatePostDto } from './dto/create-post.dto';
import { AddGalleryDto } from './dto/add-gallery.dto';
import { DelGalleryDto } from './dto/del-gallery.dto';
import config from 'src/siteConfig';

@ApiTags('我的')
@Controller(`${config.API_PREFIX}/my`)
export class MyController {
  constructor(private readonly myService: MyService) {}

  @ApiOperation({ summary: '发帖子' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member, UserLevel.Vip)
  @Post('post')
  async createPost(@Body() dto: CreatePostDto, @RequestUser() user: any) {
    return this.myService.createPost(dto, user);
  }

  @ApiOperation({ summary: '帖子列表' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member, UserLevel.Vip)
  @Get('posts')
  async getPosts(@Query() query: any, @RequestUser() user: any) {
    return this.myService.getPosts(query, user);
  }

  @ApiOperation({ summary: '删除帖子' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member, UserLevel.Vip)
  @Post('post/:id/del')
  async deletePost(@Param('id') id: string, @RequestUser() user: any) {
    return this.myService.deletePost(id, user);
  }

  @ApiOperation({ summary: '我的轨迹' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member, UserLevel.Vip)
  @Get('visits')
  async getVisits(@Query() query: any, @RequestUser() user: any) {
    return this.myService.getVisits(query, user);
  }

  @ApiOperation({ summary: '我的相册' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  @Get('gallery')
  async getGallery(@Query() query: any, @RequestUser() user: any) {
    return this.myService.getGallery(query, user);
  }

  @ApiOperation({ summary: '添加相册图片' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  @Post('gallery')
  async addGallery(@Body() dto: AddGalleryDto, @RequestUser() user: any) {
    return this.myService.addGallery(dto, user);
  }

  @ApiOperation({ summary: '删除相册图片' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  @Post('gallery/del')
  async delGallery(@Body() dto: DelGalleryDto, @RequestUser() user: any) {
    return this.myService.delGallery(dto, user);
  }

  @ApiOperation({ summary: '我的场馆' })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  @Get('venues')
  async getVenues(@Query() query: any, @RequestUser() user: any) {
    return this.myService.getVenues(query, user);
  }
} 