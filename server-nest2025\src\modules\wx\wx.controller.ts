import { Controller, Get, Post, Query, Body, UploadedFile, UseInterceptors, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { WxService } from './wx.service';
import { WxContentCheckDto } from './dto/wxContent-check.dto';

@ApiTags('微信接口')
@Controller('wxApi')
export class WxController {
  constructor(private readonly wxService: WxService) {}

  @Get('getWxOpenid')
  @ApiOperation({ summary: '获取微信 openid' })
  @ApiQuery({ name: 'code', required: true, description: '微信登录 code' })
  async getWxOpenid(@Query('code') code: string) {
    try {
      const res = await this.wxService.getOpenid(code);
      return res;
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }

  @Get('getMiniQrcode')
  @ApiOperation({ summary: '获取小程序二维码' })
  @ApiQuery({ name: 'scene', required: true, description: '场景值' })
  @ApiQuery({ name: 'page', required: false, description: '页面路径' })
  @ApiQuery({ name: 'width', required: false, description: '二维码宽度' })
  @ApiQuery({ name: 'auto_color', required: false, description: '是否自动配置颜色' })
  @ApiQuery({ name: 'line_color', required: false, description: '线条颜色' })
  @ApiQuery({ name: 'is_hyaline', required: false, description: '是否需要透明底色' })
  async getMiniQrcode(
    @Query('scene') scene: string,
    @Query('page') page?: string,
    @Query('width') width?: number,
    @Query('auto_color') auto_color?: boolean,
    @Query('line_color') line_color?: string,
    @Query('is_hyaline') is_hyaline?: boolean
  ) {
    return await this.wxService.getMiniQrcode({
      scene,
      page,
      width,
      auto_color,
      line_color,
      is_hyaline
    });
  }

  @Get('getWxAccessToken')
  @ApiOperation({ summary: '获取微信 access_token' })
  async getWxAccessToken() {
    const access_token = await this.wxService.getAccessToken();
    return { access_token };
  }

  @Post('contentCheck')
  @ApiOperation({ summary: '内容安全检查' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({ type: WxContentCheckDto })
  @UseInterceptors(FileInterceptor('file'))
  async contentCheck(
    @Query('type') type: string,
    @Body() contentCheckDto: WxContentCheckDto,
    @UploadedFile() file?: {path:string}
  ) {
    if (!type) {
      throw new BadRequestException('参数type错误');
    }

    if (type === 'txt') {
      const { content } = contentCheckDto;
      if (!content || content.trim() === '') {
        throw new BadRequestException('参数content错误');
      }
      return await this.wxService.checkTextContent(content);
    }

    if (type === 'img') {
      if (!file) {
        throw new BadRequestException('请上传图片');
      }
      return await this.wxService.checkImageContent(file);
    }

    throw new BadRequestException('不支持的检查类型');
  }
} 