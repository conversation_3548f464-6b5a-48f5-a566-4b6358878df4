//比赛种类
import { BelongsTo, BelongsToMany, Column, DataType, ForeignKey, HasMany, Table } from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import Match from './Match';
import League from './League';
import LeagueMatchClass from './LeagueMatchClass';

@Table
export default class MatchClass extends BaseUUIDEntity {
  @Column
  declare name: string

  @Column
  declare intro: string

  @Column
  declare avatar: string

  @Column(DataType.TEXT)
  declare content: string

  @Column(DataType.STRING(1000))
  declare extUrl: string

  @Column(DataType.UUID)
  declare SportId:number

  @HasMany(()=>Match)
  declare Matchs:Match[]
  

  @BelongsToMany(() => League, () => LeagueMatchClass)
  declare Leagues: League[];
}
