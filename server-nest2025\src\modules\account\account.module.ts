import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import Account from '../../db/model/Account';
import { AccountService } from './account.service';
import { AccountController } from './account.controller';

@Module({
  imports: [SequelizeModule.forFeature([Account])],
  providers: [AccountService],
  controllers: [AccountController],
})
export class AccountModule {}