import {
  Column,
  DataType,
  Default,
  Table,
} from 'sequelize-typescript';
import  BaseUUIDEntity from "./_BaseUUIDEntity";
@Table
export default class Document extends BaseUUIDEntity {
  @Column
  declare name: string;

  @Column
  declare intro: string; //简短介绍

  @Column(DataType.TEXT)
  declare content: string; //json imgs

  @Column
  declare extUrl: string;

  @Column
  declare avatar: string;

  @Column
  declare groupKey: string;

  @Column
  declare key: string;

  @Column(DataType.UUID)
  declare SportId: string;
}
