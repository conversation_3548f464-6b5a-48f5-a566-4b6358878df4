/*
openid对应到user表openid
是否需要当user 与 account做关系(1 v 1)映射的思考 2020.22.12：
好处：查询方法，结构清晰，可不必使用openid换成userId更科学
害处：改成大，耦合强
*/
import {
  BelongsToMany,
  Column,
  <PERSON><PERSON>ult,
  HasMany,
  Table
} from "sequelize-typescript";
import  BaseUUIDEntity from "./_BaseUUIDEntity";
import League from "./League";
import LeagueAccount from "./LeagueAccount";

@Table
export default class Account extends BaseUUIDEntity{
    @Column
    declare name: string

    @Column
    declare nickName: string

    @Column
    declare password: string

    @Column
    declare bak: string

    @Default("")
    @Column
    declare openid: string

    @Default(0)
    @Column
    declare type:number  //999:超级管理员//admin,leader,teamer
      
    @Default(0)
    @Column
    declare state: number

    // @BelongsToMany(() => League, () => LeagueAccount)
    // Leagues: League[]
}