import {
  BelongsTo,
  Column,
  DataType,
  <PERSON><PERSON>ult,
  ForeignKey,
  Table,
} from 'sequelize-typescript';
import  BaseUUIDEntity from './_BaseUUIDEntity';
import League from './League';
import Team from './Team';

@Table
export default class LeagueTeam extends BaseUUIDEntity {
  @Default(0)
  @Column(DataType.DECIMAL)
  declare score: number
  
  @Column(DataType.STRING(1000))
  declare content:string

  @Column(DataType.UUID)
  declare SportId:number

  @ForeignKey(()=>League)
  @Column(DataType.UUID)
  declare LeagueId:string
  @BelongsTo(()=>League,{constraints:false,foreignKey:"LeagueId"})
  declare League:League
  
  @ForeignKey(()=>Team)
  @Column(DataType.UUID)
  declare TeamId:string
  @BelongsTo(()=>Team,{constraints:false,foreignKey:"TeamId"})
  declare Team:Team
}