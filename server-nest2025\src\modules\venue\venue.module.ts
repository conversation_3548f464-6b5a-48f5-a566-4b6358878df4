import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { VenueController } from './venue.controller';
import { VenueService } from './venue.service';
import { IndexModule } from '../index/index.module';
import Venue from '../../db/model/Venue';
import VenueStatic from '../../db/model/VenueStatic';
import User from '../../db/model/User';
import UserLikeLog from '../../db/model/UserLikeLog';
import VenueVisitLog from '../../db/model/VenueVisitLog';
import VenueHistory from '../../db/model/VenueHistory';
import UserPost from '../../db/model/UserPost';
import UserGallery from '../../db/model/UserGallery';
import { AuthService } from '../auth/auth.service';
import Account from '../../db/model/Account';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Venue,
      VenueStatic,
      User,
      UserLikeLog,
      VenueVisitLog,
      VenueHistory,
      UserPost,
      UserGallery,
      Account
    ]),
    IndexModule
  ],
  controllers: [VenueController],
  providers: [VenueService,AuthService],
  exports: [VenueService]
})
export class VenueModule {} 