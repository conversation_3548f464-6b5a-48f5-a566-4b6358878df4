import { Controller, Get, Post, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiParam } from '@nestjs/swagger';
import { VenueService } from './venue.service';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { CreateOrUpdateVenueDto } from './dto/create-update-venue.dto';
import { VisitVenueDto } from './dto/visit-venue.dto';
import { PostAlbumDto } from './dto/post-album.dto';
import { AccountType } from 'src/utils/enums';
import { RequestUser } from 'src/auth/decorators/requestUser.decorator';
import { RequestAccountUser } from 'src/auth/decorators/acountUser.decorator';
import config from 'src/siteConfig';

@ApiTags('场馆')
@Controller(`${config.API_PREFIX}/venue`)
export class VenueController {
  constructor(private readonly venueService: VenueService) {}

  @Get()
  @ApiOperation({ summary: '获取场馆列表' })
  async getVenues(@Query() query: {divisionCode?: number,province?: string,city?: string,pageIndex?: number,pageSize?: number}) {
    return await this.venueService.getVenues(query);
  }

  @Get('top')
  @ApiOperation({ summary: '获取场馆top' })
  async getTop(@Query() query: {divisionCode?: number,top?: number,isRandom?:boolean}) {
    return await this.venueService.getTop(query);
  }

  // @Get('venuesForSelect')
  // @ApiOperation({ summary: '获取场馆选择列表' })
  // @ApiQuery({ name: 'where', required: true, description: '查询条件' })
  // async getVenuesForSelect(@Query('where') where: string) {
  //   return await this.venueService.getVenuesForSelect(where);
  // }

  @Get(':id')
  @ApiOperation({ summary: '获取场馆详情' })
  @ApiParam({ name: 'id', required: true, description: '场馆ID' })
  async getVenue(@Param('id') id: string, @RequestUser() user?: any) {
    return await this.venueService.getVenue(id, user);
  }

  @Post(':id/del')
  @UseGuards(RoleGuard)
  @Roles(AccountType.Operator, AccountType.Admin)
  @ApiOperation({ summary: '删除场馆' })
  @ApiParam({ name: 'id', required: true, description: '场馆ID' })
  async deleteVenue(@Param('id') id: string, @RequestAccountUser([AccountType.Operator, AccountType.Admin]) user?: any) {
    return await this.venueService.deleteVenue(id, user);
  }

  @Post()
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '创建/编辑场馆' })
  async createOrUpdateVenue(@Body() createVenueDto: CreateOrUpdateVenueDto, @RequestUser() user?: any) {
    return await this.venueService.createOrUpdateVenue(createVenueDto, user);
  }

  @Delete()
  @UseGuards(RoleGuard)
  @Roles(AccountType.Operator, AccountType.Admin)
  @ApiOperation({ summary: '删除场馆' })
  @ApiQuery({ name: 'id', required: true, description: '场馆ID' })
  async removeVenue(@Query('id') id: string,@RequestAccountUser([AccountType.Operator, AccountType.Admin]) user?: any) {
    return await this.venueService.removeVenue(id);
  }

  @Post(':id/like')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '喜欢/取消喜欢场馆' })
  @ApiParam({ name: 'id', required: true, description: '场馆ID' })
  async likeVenue(@Param('id') id: string, @RequestUser() user?: any) {
    return await this.venueService.likeVenue(id, user);
  }

  @Post(':id/visit')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '场馆签到' })
  @ApiParam({ name: 'id', required: true, description: '场馆ID' })
  async visitVenue(@Param('id') id: string, @Body() visitVenueDto: VisitVenueDto, @RequestUser() user?: any) {
    return await this.venueService.visitVenue(id, visitVenueDto, user);
  }

  @Get(':id/visits')
  @ApiOperation({ summary: '获取场馆访问记录' })
  @ApiParam({ name: 'id', required: true, description: '场馆ID' })
  async getVenueVisits(@Param('id') id: string, @Query() query: any) {
    return await this.venueService.getVenueVisits(id, query);
  }

  @Get(':id/updateHistory')
  @ApiOperation({ summary: '获取场馆更新历史' })
  @ApiParam({ name: 'id', required: true, description: '场馆ID' })
  async getVenueUpdateHistory(@Param('id') id: string, @Query() query: any) {
    return await this.venueService.getVenueUpdateHistory(id, query);
  }

  @Post('postAlbum')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '发布场地图库' })
  async postAlbum(@Body() postAlbumDto: PostAlbumDto, @RequestUser() user?: any) {
    return await this.venueService.postAlbum(postAlbumDto, user);
  }
} 