import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Reflector } from '@nestjs/core';
import { SKIP_RESPONSE_FORMAT } from './skip-response-format.decorator';

export interface Response<T> {
  code: number;
  msg: string;
  data: T;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    const skipFormat = this.reflector.get<boolean>(
      SKIP_RESPONSE_FORMAT,
      context.getHandler(),
    );

    if (skipFormat) {
      return next.handle();
    }

    return next.handle().pipe(
      map(data => {
        // 如果已经是标准格式，直接返回
        if (data && typeof data === 'object' && 'code' in data && 'msg' in data && 'data' in data) {
          return data;
        }
        
        // 如果是布尔值，转换为标准格式
        if (typeof data === 'boolean') {
          return {
            code: data ? 0 : -100,
            msg: data ? '成功' : '失败',
            data: data
          };
        }
        
        // 如果是字符串，转换为标准格式
        if (typeof data === 'string') {
          return {
            code: 0,
            msg: '成功',
            data: data
          };
        }
        
        // 如果是数字，转换为标准格式
        if (typeof data === 'number') {
          return {
            code: 0,
            msg: '成功',
            data: data
          };
        }
        
        // 如果是对象或数组，转换为标准格式并处理字段名
        const processedData = this.convertKeysToCamelCase(data);
        return {
          code: 0,
          msg: '成功',
          data: processedData
        };
      }),
    );
  }

  /**
   * 将对象的所有字段名转换为小写开头
   */
  private convertKeysToCamelCase(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.convertKeysToCamelCase(item));
    }

    if (typeof obj === 'object') {
      const result: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const newKey = this.toCamelCase(key);
          result[newKey] = this.convertKeysToCamelCase(obj[key]);
        }
      }
      return result;
    }

    return obj;
  }

  /**
   * 将字符串转换为小写开头
   */
  private toCamelCase(str: string): string {
    if (!str) return str;
    return str.charAt(0).toLowerCase() + str.slice(1);
  }
} 