import {
  <PERSON>ongsTo,
  Column,
  DataType,
  <PERSON><PERSON>ult,
  Foreign<PERSON>ey,
  Table,
} from 'sequelize-typescript';
import  BaseUUIDEntity from './_BaseUUIDEntity';
import MatchTeam from './MatchTeam';
import Player from './Player';
import Match from './Match';

@Table({timestamps:false})
export default class MatchTeamPlayer extends BaseUUIDEntity {
  @Default(0)
  @Column(DataType.DECIMAL)
  declare score:number

  @ForeignKey(()=>MatchTeam)
  @Column(DataType.UUID)
  declare MatchTeamId:string

  @ForeignKey(()=>Player)
  @Column(DataType.UUID)
  declare PlayerId:string

  @ForeignKey(() => Match)
  @Column(DataType.UUID)
  declare MatchId: string;

  @BelongsTo(() => Match, { foreignKey: 'MatchId' })
  declare Match: Match;
  
  

}