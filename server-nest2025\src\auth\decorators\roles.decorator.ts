import { SetMetadata } from '@nestjs/common';
import { AccountType, UserLevel } from '../../utils/enums';

export const ROLES_KEY = 'roles';
export const ACCOUNT_TYPES_KEY = 'accountTypes';
export const USER_LEVELS_KEY = 'userLevels';

export const Roles = (...roles: (AccountType | UserLevel)[]) => {
  const accountTypes = roles.filter(role => Object.values(AccountType).includes(role as AccountType));
  const userLevels = roles.filter(role => Object.values(UserLevel).includes(role as UserLevel));

  return (target: any, key?: string, descriptor?: any) => {
    if (accountTypes.length > 0) {
      SetMetadata(ACCOUNT_TYPES_KEY, accountTypes)(target, key || '', descriptor);
    }
    if (userLevels.length > 0) {
      SetMetadata(USER_LEVELS_KEY, userLevels)(target, key || '', descriptor);
    }
  };
};