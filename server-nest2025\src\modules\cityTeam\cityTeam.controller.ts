import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiBody, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CityTeamService } from './cityTeam.service';
import { RoleGuard } from '../../auth/guards/role.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { RequestAccountUser } from '../../auth/decorators/acountUser.decorator';
import { RequestUser } from '../../auth/decorators/requestUser.decorator';
import { UserLevel, AccountType } from '../../utils/enums';
import { CityTeamDto } from './dto/cityTeam.dto';
import { QueryCityTeamDto } from './dto/query-cityTeam.dto';
import config from 'src/siteConfig';

@ApiTags('城市队')
@Controller(`${config.API_PREFIX}/cityteam`)
export class CityTeamController {
  constructor(private readonly cityTeamService: CityTeamService) {}

    /**
   * 获取城市队列表
   * @param createCityTeamDto 
   * @param user 
   * @returns 
   */
  @ApiOperation({ 
    summary: '获取城市队列表',
    description: '分页获取城市队列表，支持关键词搜索、类型筛选、地区筛选等'
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        rows: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number' },
              name: { type: 'string' },
              intro: { type: 'string' },
              avatar: { type: 'string' },
              content: { type: 'string' },
              type: { type: 'number' },
              country: { type: 'string' },
              province: { type: 'string' },
              city: { type: 'string' },
              district: { type: 'string' },
              address: { type: 'string' },
              leader: { type: 'string' },
              leaderLink: { type: 'string' },
              images: { type: 'string' },
              rank: { type: 'number' },
              createdAt: { type: 'string' },
              updatedAt: { type: 'string' }
            }
          }
        },
        count: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  @Get()
  async getCityTeams(@Query() query: QueryCityTeamDto) {
    return await this.cityTeamService.getCityTeams(query);
  }

    /**
   * 获取城市队top
   */
  @ApiOperation({ 
    summary: '获取城市队top',
    description: '获取城市队top，支持地区筛选、随机排序'
  })
  @ApiQuery({ name: 'divisionCode', description: '地区代码', required: false })
  @ApiQuery({ name: 'top', description: '返回数量', required: false })
  @ApiQuery({ name: 'isRandom', description: '是否随机排序', required: false })
  @Get('top')
  async getTop(@Query() query: {divisionCode?: number,top?: number,isRandom?:boolean}) {
    return await this.cityTeamService.getTop(query);
  }

  /**
   * 根据ID获取城市队详情
   */
  @ApiOperation({ 
    summary: '根据ID获取城市队详情',
    description: '根据城市队ID获取详细信息，包括关联的运动类型和创建者信息'
  })
  @ApiParam({ name: 'id', description: '城市队ID', example: 1 })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功'
  })
  @ApiResponse({ 
    status: 404, 
    description: '城市队不存在'
  })
  @Get(':id')
  async getCityTeamById(@Param('id') id: string) {
    return await this.cityTeamService.getCityTeamById(id);
  }

  /**
   * 创建/更新城市队
   */
  @ApiOperation({ 
    summary: '创建/更新城市队',
    description: '创建新的城市队，需要注册用户权限，如果id存在则更新城市队'
  })
  @ApiResponse({ 
    status: 201, 
    description: '创建成功'
  })
  @ApiResponse({ 
    status: 400, 
    description: '参数错误'
  })
  @ApiResponse({ 
    status: 401, 
    description: '未授权'
  })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member,UserLevel.Admin,UserLevel.Vip,UserLevel.Admin,AccountType.Admin)
  @Post()
  async UpdateCityTeam(
    @Body() createCityTeamDto: CityTeamDto,
    @RequestUser() user: any
  ) {
    if(createCityTeamDto.id){
      return await this.cityTeamService.createOrUpdateCityTeam(createCityTeamDto, user);
    }else{
      return await this.cityTeamService.createOrUpdateCityTeam(createCityTeamDto, user);
    }
  }


  /**
   * 删除城市队
   */
  @ApiOperation({ 
    summary: '删除城市队',
    description: '软删除城市队（设置disabled=1），只有创建者或管理员可以删除'
  })
  @ApiBearerAuth('JWT-auth')
  @ApiParam({ name: 'id', description: '城市队ID', example: 1 })
  @ApiResponse({ 
    status: 200, 
    description: '删除成功'
  })
  @ApiResponse({ 
    status: 403, 
    description: '没有权限'
  })
  @ApiResponse({ 
    status: 404, 
    description: '城市队不存在'
  })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  @Delete(':id')
  async deleteCityTeam(
    @Param('id') id: string,
    @RequestAccountUser() user: ITokenUser
  ) {
    return await this.cityTeamService.deleteCityTeam(id, user);
  }

    /**
   * 设置城市队排序
   */
  @ApiOperation({ 
    summary: '设置城市队排序',
    description: '设置城市队的排序权重，数值越大排序越靠前'
  })
  @ApiBearerAuth('JWT-auth')
  @ApiParam({ name: 'id', description: '城市队ID', example: 1 })
  @ApiBody({ 
    schema: { 
      type: 'object',
      properties: { 
        rank: { 
          type: 'number',
          description: '排序权重',
          example: 100
        } 
      },
      required: ['rank']
    } 
  })
  @ApiResponse({ 
    status: 200, 
    description: '设置成功'
  })
  @ApiResponse({ 
    status: 403, 
    description: '没有权限'
  })
  @ApiResponse({ 
    status: 404, 
    description: '城市队不存在'
  })
  @UseGuards(RoleGuard)
  @Roles(UserLevel.Member)
  @Post(':id/rank')
  async setRank(
    @Param('id') id: string,
    @Body('rank') rank: number,
    @RequestAccountUser() user: ITokenUser
  ) {
    return await this.cityTeamService.setRank(id, rank, user);
  }

      /**
   * 根据运动类型获取城市队
   */
  @ApiOperation({ 
    summary: '根据运动类型获取城市队',
    description: '根据运动类型ID获取相关的城市队列表'
  })
  @ApiParam({ name: 'sportId', description: '运动类型ID', example: '123e4567-e89b-12d3-a456-************' })
  @ApiQuery({ 
    name: 'limit', 
    description: '返回数量', 
    required: false,
    example: 10
  })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功'
  })
  @Get('sport/:sportId')
  async getCityTeamsBySport(
    @Param('sportId') sportId: string,
    @Query('limit') limit?: number
  ) {
    return await this.cityTeamService.getCityTeamsBySport(sportId, limit);
  }
} 