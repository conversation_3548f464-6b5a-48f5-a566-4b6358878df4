//约毽-用户应约意向
import {
  Column,
  DataType,
  Default,
  Table,
  Model,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  BelongsTo
} from 'sequelize-typescript';
import Party from './Party';
import User from './User';

@Table
export default class PartyInterest extends Model {

  @Column
  declare ip: string

  @Column
  declare isInterest: number //1：有意向，0:无意向

  @ForeignKey(() => Party)
  @Column(DataType.UUID)
  declare PartyId: string
  @BelongsTo(()=>Party,{constraints:false})
  declare Party:Party

  @ForeignKey(() => User)
  @Column(DataType.UUID)
  declare UserId: string
  @BelongsTo(()=>User,{constraints:false})
  declare User:User
  
}
