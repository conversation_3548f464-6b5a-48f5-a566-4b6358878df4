import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, IsArray } from 'class-validator';

export class CreateOrUpdateVenueDto {
  @ApiPropertyOptional({ description: '场馆ID' })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiProperty({ description: '场馆名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '场馆地址' })
  @IsString()
  address: string;

  @ApiProperty({ description: '省份' })
  @IsString()
  province: string;

  @ApiProperty({ description: '城市' })
  @IsString()
  city: string;

  @ApiProperty({ description: '区划代码' })
  @IsOptional()
  divisionCode?: number;

  @ApiPropertyOptional({ description: '经度' })
  @IsOptional()
  lon?: string;

  @ApiPropertyOptional({ description: '纬度' })
  @IsOptional()
  lat?: string;

  @ApiPropertyOptional({ description: '场馆介绍' })
  @IsOptional()
  @IsString()
  intro?: string;

  @ApiPropertyOptional({ description: '场馆图片' })
  @IsOptional()
  @IsString()
  images?: string;

  @ApiPropertyOptional({ description: '排序' })
  @IsOptional()
  @IsNumber()
  rank?: number;

  @ApiPropertyOptional({ description: '场馆规模' })
  @IsOptional()
  size?: number;

  @ApiPropertyOptional({ description: '场馆类型' })
  @IsOptional()
  type?: number;

  @ApiPropertyOptional({ description: '停车' })
  @IsOptional()
  parking?: number;

  @ApiPropertyOptional({ description: '星级' })
  @IsOptional()
  star?: number;

  @ApiPropertyOptional({ description: '使用时段' })
  @IsOptional()
  able?: number;

  @ApiPropertyOptional({ description: '付费' })
  @IsOptional()
  paid?: number;

  @ApiPropertyOptional({ description: '付费说明' })
  @IsOptional()
  @IsString()
  paidIntro?: string;

  @ApiPropertyOptional({ description: '联系人' })
  @IsOptional()
  @IsString()
  contacter ?: string;

  @ApiPropertyOptional({ description: '联系电话' })
  @IsOptional()
  @IsString()
  contacterPhone?: string;
} 