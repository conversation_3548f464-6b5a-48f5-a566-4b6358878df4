import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import Document from '../../db/model/Document';
import { QueryDocumentDto } from './dto/query-document.dto';
import { queryPaginDataAsync } from '../../utils/funs';

@Injectable()
export class DocumentService {
  constructor(
    @InjectModel(Document)
    private documentModel: typeof Document,
  ) {}

  async getDocuments(query: QueryDocumentDto) {
    const result = await queryPaginDataAsync(
      this.documentModel,
      query,
      {},
      true
    );
    return result;
  }

  async getDocument(id: string) {
    const document = await this.documentModel.findByPk(id, {
      attributes: ['id', 'name', 'avatar', 'intro', 'content']
    });
    return document;
  }
}