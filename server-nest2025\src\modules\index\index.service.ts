import { Injectable, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Includeable, QueryTypes, WhereOptions } from 'sequelize';
import  Sport  from '../../db/model/Sport';
import  MatchClass  from '../../db/model/MatchClass';
import  Asset  from '../../db/model/Asset';
import  Sys from '../../db/model/Sys';
import  League  from '../../db/model/League';
import  Match from '../../db/model/Match';
import  MatchTeamResult  from '../../db/model/MatchTeamResult';
import  LeagueTeamResult  from '../../db/model/LeagueTeamResult';
import  Team  from '../../db/model/Team';
import CityTeam from 'src/db/model/CityTeam';
import Venue from 'src/db/model/Venue';
import User from 'src/db/model/User';
import { Sequelize } from 'sequelize-typescript'
import { getCityDivisionRange } from 'src/utils/funs';
import CacheData, { ICacheData }  from './CacheData';



  @Injectable()
  export class IndexService implements OnModuleInit {
    public cacheData: ICacheData = CacheData;
  
    constructor(
      @InjectModel(Sport) private sportModel: typeof Sport,
      @InjectModel(MatchClass) private matchClassModel: typeof MatchClass,
      @InjectModel(Asset) private assetModel: typeof Asset,
      @InjectModel(Sys) private sysModel: typeof Sys,
      @InjectModel(League) private leagueModel: typeof League,
      @InjectModel(Match) private matchModel: typeof Match,
      @InjectModel(MatchTeamResult) private matchTeamResultModel: typeof MatchTeamResult,
      @InjectModel(LeagueTeamResult) private leagueTeamResultModel: typeof LeagueTeamResult,
      @InjectModel(Team) private teamModel: typeof Team,
      @InjectModel(Venue) private venueModel: typeof Venue,
      @InjectModel(User) private userModel: typeof User,
      @InjectModel(CityTeam) private cityTeamModel: typeof CityTeam,
      private readonly sequelize: Sequelize,
    ) {
      this.setupHooks();
    }
  
    async onModuleInit() {
      await this.updateAll();
    }

    // 公共方法
    getCacheData() {
      return this.cacheData;
    }
  
    public async updateSports() {
      this.cacheData.sports = (await this.sportModel.findAll()).map(v => v.get());

      console.log(this.cacheData.sports);

      this.cacheData.SportId = this.cacheData.sports[0]?.id;
      this.cacheData.matchClasses = (await this.matchClassModel.findAll({
        attributes: ['id', 'name', 'rank', 'avatar']
      })).map(v => v.get());
    }
  
    public async updateAsset() {
      this.cacheData.assets = (await this.assetModel.findAll({
        attributes: ['name', 'key', 'content', 'intro'],
        where: {
          SportId: this.cacheData.SportId,
          disabled: 0
        }
      })).map(v => v.get());
    }
  
    public async updateSys() {
      const sys = await this.sysModel.findAll();
      this.cacheData.sys = {};
      sys.forEach(v => {
        let value;
        try {
          value = parseInt(v.value);
        } catch {
          value = v.value;
        }
        value = isNaN(value) ? v.value : value;
        this.cacheData.sys[v.key] = value;
      });
    }
  
    private async updateNewLeague() {
      const leagues = await this.leagueModel.findAll({
        where: {
          SportId: this.cacheData.SportId,
          disabled: 0,
        },
        attributes: ['id', 'name', 'state', 'intro', 'avatar', 'beginDate', 'endDate', 'matchOverCount', 'matchCount', 'bak1', 'rank'],
        order: [['rank', 'DESC']],
        limit: 1,
        include: [{
          model: this.matchClassModel,
          attributes: ['name', 'avatar']
        }]
      });
  
      const leagueData = leagues.map(v => v.get());
      if (leagueData[0]) {
        this.cacheData.newLeague.base = leagueData[0];
      } else {
        this.cacheData.newLeague = {
          base: {},
          teams: [],
          matchs: [],
        };
      }
    }
  
    private async updateNewLeagueTeams() {
      if (!this.cacheData.newLeague.base) {
        return;
      }
  
      let teams;
      if (this.cacheData.newLeague.base.matchCount === 1) {
        teams = await this.matchTeamResultModel.findAll({
          where: {
            LeagueId: this.cacheData.newLeague.base.id
          },
          attributes: ['id', 'rank', 'score', 'content', 'updatedAt'],
          include: {
            model: this.teamModel,
            attributes: ['id', 'name', 'avatar']
          },
        });
      } else {
        teams = await this.leagueTeamResultModel.findAll({
          where: {
            LeagueId: this.cacheData.newLeague.base.id
          },
          order: [['rank', 'DESC']],
          attributes: ['id', 'rank', 'score', 'content', 'updatedAt'],
          include: {
            model: this.teamModel,
            attributes: ['id', 'name', 'avatar']
          },
        });
      }
      this.cacheData.newLeague.teams = teams.map(v => v.get());
    }
  
    private async updateNewLeagueMatchs() {
      const matchs = await this.matchModel.findAll({
        where: {
          LeagueId: this.cacheData.newLeague.base.id
        },
        order: [['rank', 'DESC']],
        attributes: ['id', 'name', 'rank', 'address', 'intro', 'longitude', 'latitude', 'beginDate', 'endDate', 'state', 'bak', 'type'],
        include: [{
          model: this.matchClassModel,
          attributes: ['id', 'name', 'avatar']
        },
        {
          model: this.matchTeamResultModel,
          attributes: ['id', 'rank', 'score', 'updatedAt', 'content'],
          include: [{
            model: this.teamModel,
            attributes: ['id', 'name', 'avatar']
          }] as Includeable[],
        }]
      });
      this.cacheData.newLeague.matchs = matchs.map(v => v.get());
    }
  
    public async updateNewLeagueMatchResult(matchResult: any) {
      if (matchResult) {
        const match = this.cacheData.newLeague.matchs.find(v => v.id === matchResult.MatchId);
        if (match) {
          const newMatchResultIdx = match.MatchTeamResults.findIndex(v => v.id === matchResult.id);
          if (newMatchResultIdx > -1) {
            Object.assign(match.MatchTeamResults[newMatchResultIdx], {
              rank: matchResult.rank,
              score: matchResult.score,
              content: matchResult.content,
            });
          }
        }
  
        const leagueTeamIdx = this.cacheData.newLeague.teams.findIndex(v => v.Team.id === matchResult.TeamId);
        if (this.cacheData.newLeague.matchs.length !== 1) {
          const teamResult = await this.leagueTeamResultModel.findOne({
            where: { TeamId: matchResult.TeamId },
            attributes: ['id', 'rank', 'score', 'content', 'updatedAt'],
            include: {
              model: this.teamModel,
              attributes: ['id', 'name', 'avatar']
            },
          });
          if (teamResult) {
            this.cacheData.newLeague.teams[leagueTeamIdx] = teamResult.get();
          }
        }
      }
    }
  
    public async updateNewLeagueAll() {
      await this.updateNewLeague();
      if (this.cacheData.newLeague.base) {
        await this.updateNewLeagueTeams();
        await this.updateNewLeagueMatchs();
      }
    }
  
    public async updateAll() {
      await this.updateSys();
      await this.updateSports();
      await this.updateNewLeagueAll();
      await this.updateAsset();
      console.log('cache complete =>');
    }
  
    private setupHooks() {
      // 设置模型钩子
      this.leagueModel.afterDestroy(async (league) => {
        if (this.cacheData.newLeague.base?.id === league.id) {
          await this.updateNewLeagueAll();
        }
      });
  
      this.leagueModel.afterCreate(async (league) => {
        if (league.disabled === 0) {
          await this.updateNewLeagueAll();
        }
      });
  
      // ... 其他钩子设置
    }
  
    public async getCityCardData(province: string, city: string,divisionCode: number,forceUpdate:boolean=false) {
      let cityCardDataIdx = -1;

      if(divisionCode) {
        cityCardDataIdx = this.cacheData.cityCards.findIndex(v => v.divisionCode === divisionCode);
      }else{
        cityCardDataIdx = this.cacheData.cityCards.findIndex(v => v.province === province && v.city === city);
      }

      if(cityCardDataIdx == -1 || forceUpdate) {
        let where = '';
        if (divisionCode) {
          const {min,max} = getCityDivisionRange(divisionCode);
          where = `divisionCode >= ${min} AND divisionCode <= ${max}`;
        } else {
          where = `province = '${province}' AND city = '${city}'`;
        }

        // 用 Sequelize 的 literal 写一个 SQL 查询获取所有统计数据
        const result = await this.sequelize.query(`
          SELECT 
            (SELECT COUNT(*) FROM Venues WHERE disabled = 0 AND ${where}) as venueCount,
            (SELECT COUNT(*) FROM Users WHERE ${where}) as memberCount,
            (SELECT COUNT(*) FROM CityTeams WHERE disabled = 0 AND ${where}) as cTeamCount
        `, {
          type: QueryTypes.SELECT,
          raw: true
        });

        const { venueCount, memberCount, cTeamCount } = result[0] as {
          venueCount: number;
          memberCount: number;
          cTeamCount: number;
        };
        const cityCardData:ICityCardData = {
          province,
          city,
          divisionCode,
          venueCount,
          memberCount,
          cTeamCount
        };
        if(cityCardDataIdx == -1) {
          this.cacheData.cityCards.push(cityCardData);
          cityCardDataIdx = this.cacheData.cityCards.length - 1;
        }else{
          this.cacheData.cityCards[cityCardDataIdx] = cityCardData;
          
        }
      }
      return this.cacheData.cityCards[cityCardDataIdx];
    }

    public async updateCityCardData(province: string, city: string,divisionCode: number) {
      await this.getCityCardData(province, city, divisionCode,true);
    }

  }