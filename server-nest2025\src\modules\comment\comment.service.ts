import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import UserComment from '../../db/model/UserComment';
import User from '../../db/model/User';
import { IndexService } from '../index/index.service';
import { CommentType } from '../../utils/enums';
import { CreateCommentDto } from './dto/create-comment.dto';
import { queryPaginDataAsync } from 'src/utils/funs';

@Injectable()
export class CommentService {
  constructor(
    @InjectModel(UserComment)
    private userCommentModel: typeof UserComment,
    @InjectModel(User)
    private userModel: typeof User,
    private indexService: IndexService,
  ) {}

  async getComments(targetId: string, query: any) {
    const cacheData = this.indexService.getCacheData();
    if (cacheData.sys['allowComment'] !== 1) {
      return { list: [], total: 0 };
    }
    return await queryPaginDataAsync(
      this.userCommentModel,
      query,
      {
        where: { targetId },
        order: [['updatedAt', 'DESC']],
        include: [{
          model: this.userModel,
          attributes: ['nickName', 'id', 'avatar']
        }],
      }
    )
  }

  async createComment(createCommentDto: CreateCommentDto, userId: string) {
    const cacheData = this.indexService.getCacheData();
    
    if (cacheData.sys['allowComment'] !== 1) {
      throw new BadRequestException('提交错误');
    }

    const comment = {
      ...createCommentDto,
      targetType: CommentType.Venue,
      UserId: userId,
      deepth: createCommentDto.parentId ? (createCommentDto.deepth || 0) + 1 : 0
    };

    return await this.userCommentModel.create(comment);
  }

  async coverComment(commentId: string) {
    const comment = await this.userCommentModel.findByPk(commentId);
    if (!comment) {
      throw new BadRequestException('评论不存在');
    }
    
    comment.cover = 1;
    await comment.save();
    return true;
  }

  async disableComment(commentId: string) {
    const comment = await this.userCommentModel.findByPk(commentId);
    if (!comment) {
      throw new BadRequestException('评论不存在');
    }
    
    comment.disabled = 1;
    await comment.save();
    return true;
  }
} 