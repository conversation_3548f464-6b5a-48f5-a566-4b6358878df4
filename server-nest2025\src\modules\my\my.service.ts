import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import UserPost from '../../db/model/UserPost';
import UserGallery from '../../db/model/UserGallery';
import VenueVisitLog from '../../db/model/VenueVisitLog';
import UserLikeLog from '../../db/model/UserLikeLog';
import Venue from '../../db/model/Venue';
import { IndexService } from '../index/index.service';
import { LikeType } from '../../utils/enums';
import { CreatePostDto } from './dto/create-post.dto';
import { AddGalleryDto } from './dto/add-gallery.dto';
import { DelGalleryDto } from './dto/del-gallery.dto';

@Injectable()
export class MyService {
  constructor(
    @InjectModel(UserPost) private userPostModel: typeof UserPost,
    @InjectModel(UserGallery) private userGalleryModel: typeof UserGallery,
    @InjectModel(VenueVisitLog) private venueVisitLogModel: typeof VenueVisitLog,
    @InjectModel(UserLikeLog) private userLikeLogModel: typeof UserLikeLog,
    @InjectModel(Venue) private venueModel: typeof Venue,
    private indexService: IndexService,
  ) {}

  async createPost(dto: CreatePostDto, user: any) {
    const cacheData = this.indexService.getCacheData();
    if (cacheData.sys['allowPost'] === '0') {
      throw new BadRequestException('发帖功能已关闭');
    }
    const post = { ...dto, UserId: user.id };
    const _post = await this.userPostModel.create(post);
    return _post;
  }

  async getPosts(query: any, user: any) {
    const pageIndex = parseInt(query.pageIndex) || 1;
    const pageSize = parseInt(query.pageSize) || 20;
    const { count, rows } = await this.userPostModel.findAndCountAll({
      where: { UserId: user.id },
      order: [['updatedAt', 'DESC']],
      offset: (pageIndex - 1) * pageSize,
      limit: pageSize,
    });
    return {
      list: rows,
      total: count,
      pageSize,
      pageIndex,
    };
  }

  async deletePost(id: string, user: any) {
    const res = await this.userPostModel.destroy({
      where: { UserId: user.id, id },
    });
    return res;
  }

  async getVisits(query: any, user: any) {
    const pageIndex = parseInt(query.pageIndex) || 1;
    const pageSize = parseInt(query.pageSize) || 20;
    const { count, rows } = await this.venueVisitLogModel.findAndCountAll({
      where: { UserId: user.id },
      order: [['updatedAt', 'DESC']],
      offset: (pageIndex - 1) * pageSize,
      limit: pageSize,
    });
    // 兼容 venueName
    const list = rows.map((v: any) => {
      const obj = v.get ? v.get() : v;
      obj.venueName = obj.name;
      return obj;
    });
    return {
      list,
      total: count,
      pageSize,
      pageIndex,
    };
  }

  async getGallery(query: any, user: any) {
    const pageIndex = parseInt(query.pageIndex) || 1;
    const pageSize = parseInt(query.pageSize) || 20;
    const { count, rows } = await this.userGalleryModel.findAndCountAll({
      where: { UserId: user.id },
      order: [['updatedAt', 'DESC']],
      offset: (pageIndex - 1) * pageSize,
      limit: pageSize,
    });
    return {
      list: rows,
      total: count,
      pageSize,
      pageIndex,
    };
  }

  async addGallery(dto: AddGalleryDto, user: any) {
    const imageUrls = dto.imageUrls || [];
    const dbImageCount = await this.userGalleryModel.count({ where: { UserId: user.id } });
    if (dbImageCount + imageUrls.length > 24) {
      throw new BadRequestException('图片即将超出24张限制，请先整理相册');
    }
    const existUrls = await this.userGalleryModel.findAll({
      where: {
        UserId: user.id,
        url: imageUrls,
      },
    });
    const existUrlSet = new Set(existUrls.map((v: any) => v.url));
    const data = imageUrls.filter((v) => !existUrlSet.has(v)).map((v) => ({
      url: v,
      UserId: user.id,
      rank: Date.now(),
      type: 'img',
    }));
    const res = await this.userGalleryModel.bulkCreate(data);
    return res;
  }

  async delGallery(dto: DelGalleryDto, user: any) {
    const imageUrls = dto.imageUrls || [];
    const imageIds = dto.imageIds || [];
    const orArr = [
      ...imageUrls.map((v) => ({ url: v })),
      ...imageIds.map((v) => ({ id: v })),
    ];
    const res = await this.userGalleryModel.destroy({
      where: {
        UserId: user.id,
        $or: orArr,
      },
    });
    return res;
  }

  async getVenues(query: any, user: any) {
    const pageIndex = parseInt(query.pageIndex) || 1;
    const pageSize = parseInt(query.pageSize) || 20;
    const { count, rows } = await this.userLikeLogModel.findAndCountAll({
      where: {
        UserId: user.id,
        type: LikeType.Venue,
      },
      attributes: ['id', 'targetId', 'updatedAt'],
      order: [['updatedAt', 'DESC']],
      offset: (pageIndex - 1) * pageSize,
      limit: pageSize,
      include: [
        {
          model: this.venueModel,
          attributes: ['id', 'name', 'address', 'images'],
        },
      ],
    });
    return {
      list: rows,
      total: count,
      pageSize,
      pageIndex,
    };
  }
} 