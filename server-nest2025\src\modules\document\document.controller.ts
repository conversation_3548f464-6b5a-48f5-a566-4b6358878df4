import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery } from '@nestjs/swagger';
import { DocumentService } from './document.service';
import { QueryDocumentDto } from './dto/query-document.dto';
import config from 'src/siteConfig';

@ApiTags('文档')
@Controller(`${config.API_PREFIX}/document`)
export class DocumentController {
  constructor(private readonly documentService: DocumentService) {}

  @ApiOperation({ summary: '获取文档列表' })
  @Get('documents')
  async getDocuments(@Query() query: QueryDocumentDto) {
    return await this.documentService.getDocuments(query);
  }

  @ApiOperation({ summary: '获取文档详情' })
  @ApiQuery({ name: 'id', description: '文档ID' })
  @Get()
  async getDocument(@Query('id') id: string) {
    return await this.documentService.getDocument(id);
  }
}