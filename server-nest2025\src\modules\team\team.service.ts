import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import Team from '../../db/model/Team';
import { queryPaginDataAsync } from '../../utils/funs';

@Injectable()
export class TeamService {
  constructor(
    @InjectModel(Team)
    private teamModel: typeof Team,
  ) {}

  async getTeams() {
    return await queryPaginDataAsync(
      this.teamModel,
      {},
      {
        attributes: ['id', 'name', 'avatar', 'intro', 'extUrl']
      },
      true
    );
  }

  async getTeam(id: string) {
    return await this.teamModel.findByPk(id);
  }
} 