import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TeamController } from './team.controller';
import { TeamService } from './team.service';
import Team from '../../db/model/Team';

@Module({
  imports: [
    SequelizeModule.forFeature([Team])
  ],
  controllers: [TeamController],
  providers: [TeamService],
  exports: [TeamService]
})
export class TeamModule {} 