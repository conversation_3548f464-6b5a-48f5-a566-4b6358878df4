import { Column, DataType, Default, HasOne, Table, BelongsTo } from 'sequelize-typescript';
import BaseUUIDEntity from './_BaseUUIDEntity';
import PartyStatic from './PartyStatic';
import User from './User';

@Table
export default class Party extends BaseUUIDEntity {
  @Column
  declare name: string;
  
  @Column(DataType.TEXT)
  declare content: string;

  @Column
  declare address: string;

  @Column
  declare province: string; //省

  @Column
  declare city: string; //市

  @Column(DataType.INTEGER)
  declare divisionCode: number; //行政编码

  @Column(DataType.DATE)
  declare beginDate: Date;

  @Column
  declare lat: string; //地址坐标

  @Column
  declare lon: string; //地址坐标

  @Column
  declare ip: string;

  @Column
  declare type: number; //活动类型

  @Column
  declare size: number; //规模

  @Column
  declare time: number; //活动用时

  @Column
  declare parking: number; //停车情况

  @Column
  declare paid: number; //付费情况

  @Column
  declare venueType: number; //场地类型

  @Column
  declare contacter: string;

  @Column
  declare contacterPhone: string;

  @Default(0)
  @Column
  declare state: number; //0:未开始,1:进行中,2:结束

  @Column(DataType.UUID)
  declare VenueId: string;

  @Column(DataType.UUID)
  declare UserId: string;
  
  @HasOne(()=>PartyStatic,{foreignKey:'id'})
  declare Static:PartyStatic

  @BelongsTo(() => User, { foreignKey: 'UserId' })
  declare User: User;
}
