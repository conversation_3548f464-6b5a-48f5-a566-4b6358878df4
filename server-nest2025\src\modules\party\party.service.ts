import { Injectable, ForbiddenException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import Party from '../../db/model/Party';
import PartyStatic from '../../db/model/PartyStatic';
import User from '../../db/model/User';
import UserLikeLog from '../../db/model/UserLikeLog';
import PartyInterest from '../../db/model/PartyInterest';
import { IndexService } from '../index/index.service';
import { AccountType, UserLevel, LikeType } from '../../utils/enums';
import { CreatePartyDto } from './dto/create-party.dto';
import { getCityDivisionRange, pickDefinedFields } from '../../utils/funs';
import { queryPaginDataAsync } from '../../utils/funs';
import { Op } from 'sequelize';

@Injectable()
export class PartyService {
  constructor(
    @InjectModel(Party) private partyModel: typeof Party,
    @InjectModel(PartyStatic) private partyStaticModel: typeof PartyStatic,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(UserLikeLog) private userLikeLogModel: typeof UserLikeLog,
    @InjectModel(PartyInterest) private partyInterestModel: typeof PartyInterest,
    private indexService: IndexService,
  ) {}

  async getPartys(query: {divisionCode?: number,dateStart?: Date,dateEnd?: Date,order?: string,pageIndex?: number,pageSize?: number}) {
    //console.log('query',query);
    let where: any = {
      disabled: 0,
    };
    let order = [['beginDate', 'DESC']];
    if(query.divisionCode){
      const {max,min} = getCityDivisionRange(query.divisionCode);
      where.divisionCode = {
        [Op.between]: [min,max]
      };
    }
    if(query.dateStart && query.dateEnd){
        where.beginDate = {
          [Op.between]: [query.dateStart,query.dateEnd]
        };
    }else{
      // dateStart 之后的记录
      if(query.dateStart){  
        where.beginDate = {
          [Op.gte]: query.dateStart
        };
      }
      // dateEnd 之前的记录
      if(query.dateEnd){
        where.beginDate = {
          [Op.lte]: query.dateEnd
        };
      }
    }
    //console.log(where);
    const result = await queryPaginDataAsync(
      this.partyModel,
      query,
      {
        order,
        where,
        include: [
          { model: this.partyStaticModel },
          { model: this.userModel, attributes: ['id', 'nickName', 'avatar'] }
        ]
      },
      false
    );

    // result.list = result.list.map((item: any) => {
    //   item.isSelf = user && item.UserId === user.id;
    //   return item;
    // });

    return result;

  }

  async getParty(id: string, user: any) {
    let data: any = await this.partyModel.findByPk(id, {
      include: [
        { model: this.partyStaticModel },
        { model: this.userModel, attributes: ['id', 'nickName', 'avatar'] }
      ]
    });
    if (!data) throw new BadRequestException('活动不存在0');
    if (data.PartyStatic) {
      data.PartyStatic.hotCount++;
      await data.PartyStatic.save();
    }
    data = data.toJSON();
    if (user && user.id) {
      const likeCount = await this.userLikeLogModel.count({ where: { targetId: id, type: LikeType.Party, UserId: user.id } });
      const interestCount = await this.partyInterestModel.count({ where: { PartyId: id, isInterest: 1, UserId: user.id } });
      data.isLike = likeCount !== 0;
      data.isInterest = interestCount !== 0;
      data.isSelf = user.id === data.UserId;
    }
    return data;
  }

  async deleteParty(id: string, user: any) {
    const party = await this.partyModel.findByPk(id);
    if (!party) throw new BadRequestException('活动不存在1');
    if (party.UserId !== user.id) {
      // 不是本人，校验管理员权限
      if (![AccountType.Operator, AccountType.Admin].includes(user.type)) {
        throw new ForbiddenException('无权限删除');
      }
    }
    party.disabled = 1;
    await party.save();
    return true;
  }

  async createOrUpdateParty(dto: CreatePartyDto, user: {id:string,ip:string}): Promise<Partial<Party>> {
    const cacheData = this.indexService.getCacheData();
    if (cacheData.sys['allowPushParty'] !== '1') {
      throw new ForbiddenException('操作拒绝');
    }
    if (!dto.beginDate) throw new BadRequestException('时间不能为空');
    let item: Party|null;
    if (dto.id) {
      // 更新
      item = await this.partyModel.findOne({ where: { id: dto.id } });
      if (!item) throw new BadRequestException('活动不存在2');
      Object.assign(item, pickDefinedFields<CreatePartyDto>(dto));
      await item.save();
    } else {
      // 新建
      const createData: any = {
        ...pickDefinedFields<CreatePartyDto>(dto),
        UserId: user.id,
        ip: user.ip,
        Static: { likeCount: 0, shareCount: 0, interestCount: 0, hotCount: 0 }
      };
      item = await this.partyModel.create(createData, { include: [this.partyStaticModel] });
    }

    // 转为普通对象
    const result = item.toJSON ? item.toJSON() : item;

    // 删除不想暴露的字段
    delete result.disabled;
    delete result.rank;
    delete result.createdAt;
    delete result.updatedAt;
    // ...如有更多字段，继续delete

    return result;
  }

  async likeParty(id: string, user: any) {
    let log = await this.userLikeLogModel.findOne({ where: { UserId: user.id, targetId: id, type: LikeType.Party } });
    let partyStatic = await this.partyStaticModel.findByPk(id);
    if (!partyStatic) throw new BadRequestException('活动统计不存在');
    if (log) {
      partyStatic.likeCount -= 1;
      await log.destroy();
    } else {
      partyStatic.likeCount += 1;
      await this.userLikeLogModel.create({ UserId: user.id, targetId: id, type: LikeType.Party });
    }
    await partyStatic.save();
    return { result: !log, count: partyStatic.likeCount };
  }

  async getPartyLikes(id: string, query: any) {
    const pageIndex = parseInt(query.pageIndex) || 1;
    const pageSize = parseInt(query.pageSize) || 20;
    const { count, rows } = await this.userLikeLogModel.findAndCountAll({
      where: { targetId: id, type: LikeType.Party },
      order: [['updatedAt', 'DESC']],
      include: [{ model: this.userModel, attributes: ['nickName', 'id', 'avatar'] }],
      offset: (pageIndex - 1) * pageSize,
      limit: pageSize,
      raw: true,
      nest: true,
    });
    return { list: rows, total: count, pageSize, pageIndex };
  }

  async interestParty(id: string, user: any) {
    let log = await this.partyInterestModel.findOne({ where: { UserId: user.id, PartyId: id }, order: [['updatedAt', 'DESC']] });
    let partyStatic = await this.partyStaticModel.findByPk(id);
    if (!partyStatic) throw new BadRequestException('活动统计不存在');
    if (log) {
      log.isInterest = log.isInterest === 1 ? 0 : 1;
      await log.save();
      partyStatic.interestCount += log.isInterest === 1 ? 1 : -1;
    } else {
      await this.partyInterestModel.create({ UserId: user.id, PartyId: id, isInterest: 1 });
      partyStatic.interestCount++;
    }
    await partyStatic.save();
    return 1;
  }

  async getPartyInterests(id: string, query: any) {
    const pageIndex = parseInt(query.pageIndex) || 1;
    const pageSize = parseInt(query.pageSize) || 20;
    const { count, rows } = await this.partyInterestModel.findAndCountAll({
      where: { PartyId: id },
      order: [['updatedAt', 'DESC']],
      include: [{ model: this.userModel, attributes: ['nickName', 'id', 'avatar'] }],
      offset: (pageIndex - 1) * pageSize,
      limit: pageSize,
      raw: true,
      nest: true,
    });
    return { list: rows, total: count, pageSize, pageIndex };
  }
} 