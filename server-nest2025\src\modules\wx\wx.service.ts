import { Injectable, BadRequestException } from '@nestjs/common';
import { getOpenidAsync, getAccessToken } from '../../utils/wxHelper';
import * as request from 'request';
import * as fs from 'fs';

@Injectable()
export class WxService {

  constructor() {
  }
  
  async getOpenid(code: string): Promise<{ openid: string; session_key: string }> {
    return await getOpenidAsync(code);
  }
  async getAccessToken() {
    return await getAccessToken();
  }

  async getMiniQrcode(params: {
    scene: string;
    page?: string;
    width?: number;
    auto_color?: boolean;
    line_color?: string;
    is_hyaline?: boolean;
  }) {
    const access_token = await getAccessToken();
    const url = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${access_token}`;

    return new Promise((resolve, reject) => {
      request.post(
        {
          url,
          json: true,
          body: params
        },
        (error, response, body) => {
          if (error) {
            reject(error);
          } else {
            resolve(body);
          }
        }
      );
    });
  }


  async checkTextContent(content: string) {
    const access_token = await getAccessToken();
    const url = `https://api.weixin.qq.com/wxa/msg_sec_check?access_token=${access_token}`;

    return new Promise((resolve, reject) => {
      request.post(
        {
          url,
          json: true,
          body: { content }
        },
        (error, response, body) => {
          if (error) {
            reject(error);
          } else {
            resolve(body.errcode === 0);
          }
        }
      );
    });
  }

  async checkImageContent(file: {path:string}) {
    const access_token = await getAccessToken();
    const url = `https://api.weixin.qq.com/wxa/img_sec_check?access_token=${access_token}`;

    return new Promise((resolve, reject) => {
      const formData = {
        media: fs.createReadStream(file.path)
      };

      request.post(
        {
          url,
          formData
        },
        (error, response, body) => {
          if (error) {
            reject(error);
          } else {
            const result = JSON.parse(body);
            resolve(result.errcode === 0);
          }
        }
      );
    });
  }
} 