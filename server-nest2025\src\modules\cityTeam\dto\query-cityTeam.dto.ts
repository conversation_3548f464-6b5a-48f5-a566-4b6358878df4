import { IsOptional, IsString, IsN<PERSON>ber, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class QueryCityTeamDto {
  @ApiProperty({ description: '页码', required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiProperty({ description: '每页数量', required: false, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({ description: '运动类型ID', required: false })
  @IsOptional()
  @IsUUID()
  SportId?: string;

  @ApiProperty({ description: '类型：0固定队，1临时队', required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  type?: number;

  @ApiProperty({ description: '球队人数', required: false })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  size?: number;

  @ApiProperty({ description: '省份', required: false })
  @IsOptional()
  @IsString()
  province?: string;

  @ApiProperty({ description: '城市', required: false })
  @IsOptional()
  @IsString()
  city?: string;
} 