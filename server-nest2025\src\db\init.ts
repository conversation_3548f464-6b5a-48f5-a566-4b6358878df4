import { AccountType } from "src/utils/enums";
import {
  cryptPwd
} from "../utils/lib"
import Sys from "./model/Sys"
export async function init(db) {
  console.log('db=>init')

  let sports = await db.Sport.count();
  if (sports==0) {
    await db.Sport.create({
      name: "毽球",
      code: 'shuttlecock'
    });
  }
  let accounts =await db.Sport.count()
  if (accounts==0) {
    await db.Account.create({
      name: 'admin',
      password: cryptPwd('123456'),
      nicke: '超级管理员',
      type: AccountType.Admin,
    })
  }
  let sys =await db.Sys.count()
  if (sys==0) {
    Object.keys(Sys).forEach(async k=>{

      await db.Sys.create({
        key: k,
        value: Sys[k].value, //0:不可,1:可
        name: Sys[k].name,
        desc:Sys[k].desc,
      })
    })
  }
}