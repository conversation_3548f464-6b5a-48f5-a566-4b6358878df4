//留言
import {
  <PERSON><PERSON>sTo,
  Column,
  DataType,
  <PERSON>fault,
  Foreign<PERSON><PERSON>,
  Has<PERSON>any,
  Model,
  Table,
} from 'sequelize-typescript';
import  BaseUUIDEntity from './_BaseUUIDEntity';
import User from './User';

@Table
export default class UserComment extends BaseUUIDEntity {
    @Default(0)
    @Column
    declare deepth:number //深度


    @Column
    declare targetType:number//类型

    @Column
    declare targetId:string

    @Column(DataType.STRING(1000))
    declare content:string

    @Default(0)
    @Column
    declare cover:number //0:未涂抹 1:涂抹

    @Column
    declare ip:string

    @ForeignKey(() => User)
    @Column(DataType.UUID)
    declare UserId: string;
    
    @BelongsTo(() => User, { constraints: false})
    declare User: User;

    @ForeignKey(() => UserComment)
    @Column(DataType.UUID)
    declare ParentId: string;
    @BelongsTo(() => UserComment, { constraints: false, foreignKey: 'ParentId' })
    declare Parent: UserComment;
    
    @HasMany(()=>UserComment,'ParentId')
    declare Children:UserComment[]

    
}
