import {
  BelongsTo,
  BelongsToMany,
  Column,
  DataType,
  Default,
  ForeignKey,
  Table,
  HasMany,
} from 'sequelize-typescript';
import  BaseUUIDEntity from './_BaseUUIDEntity';
import Player from './Player';
import TeamPlayer from './TeamPlayer';
import MatchTeamResult from './MatchTeamResult';
import Sport from './Sport';

@Table
export default class Team extends BaseUUIDEntity {
    @Column
    declare name: string

    @Column
    declare intro: string

    @Column
    declare avatar: string

    @Column(DataType.STRING(2000))
    declare content: string

    @Column
    declare bak: string

    @Column(DataType.STRING(1000))
    declare extUrl: string

    @Column
    declare level: number

    @Column
    declare type: number//0:固定队，1:临时队
    
    @Default("")
    @Column
    declare province: string

    @Default("")
    @Column
    declare city: string

    @Default("")
    @Column
    declare district: string

    @Default("")
    @Column
    declare divisionCode: number

    @Default("")
    @Column
    declare address: string

    @Column
    declare manager: string

    @Column
    declare managerTel: string

    @Column
    declare leader: string
    
    @Column
    declare leaderTel: string

    @Column(DataType.STRING(2000))
    declare images: string

    @ForeignKey(() => Sport)
    @Column(DataType.UUID)
    declare SportId: string;

    @HasMany(() => MatchTeamResult, { constraints: false })
    declare MatchTeamResults: MatchTeamResult[]

    @BelongsTo(() => Sport, { foreignKey: 'SportId' })
    declare Sport: Sport;

    // @BelongsToMany(() => Player, () => TeamPlayer)
    // Players: Player[];
}