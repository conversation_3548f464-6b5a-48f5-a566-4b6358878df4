

const {checkRoleAsync,queryPaginDataAsync,parseRequestToModel} = require("./../../utils/funs")
const {AccountType} = require("./../../utils/lib")
const user = require("./user")

// let VenueCreateUser

module.exports = function (router, db, result) {
  router.get('/sport/venues', async (ctx, next) => {
    if(db.Venue.associations.createUser){
      delete db.Venue.associations.createUser
    }
    if(db.Venue.associations.updateUser){
      delete db.Venue.associations.updateUser
    }
    const account = ctx.params.account;
    var venues = await queryPaginDataAsync(db.Venue,ctx.request.query,{include:[{
      model:db.VenueStatic
    },
    {
      association:db.Venue.belongsTo(db.User, {
        foreignKey: 'createUserId',
        as:"createUser"
      }),
      attributes:['id','nickName','avatar'],
    },
    {
      association:db.Venue.belongsTo(db.User, {
        foreignKey: 'updateUserId',
        as:"updateUser"
      }),
      attributes:['id','nickName','avatar'],
    }
  ]},true);
    ctx.body = result.success(venues)
    return next()
  })
  
  router.get('/sport/venue/:id', async (ctx, next) => {
    let id = ctx.params.id;
    //如果指定as，每次需要association 附加外键对象，会报错重复添加
    // if(!VenueCreateUser){
    //   VenueCreateUser=db.Venue.belongsTo(db.User, {
    //     foreignKey: 'createUserId',
    //     as:'CreateUser'
    //   })
    // }

    let data = await db.Venue.findByPk(id,{
      include:[{
        model:db.VenueStatic
      },
      {
        association:db.Venue.belongsTo(db.User, {
          foreignKey: 'createUserId',
        }),
        attributes:['id','nickName','avatar'],
      }
    ]
    })
    data.VenueStatic.hotCount++;
    data.VenueStatic.save()
    data = data.toJSON()
    data.isLike = false;
    if(ctx.params.user.id){
      let count = await db.UserLikeLog.count({where:{targetId:id}});
      if(count!=0){
        data.isLike=true;
      }
    }

    ctx.body = result.success(data);
    return next()
  })

  router.post('/sport/venue/:id/del', async (ctx, next) => {
    let venue = await db.Venue.findByPk(id);
    venue.disabled=1;
    await venue.save()
    ctx.body = result.success();
    return next()
  })

  //新建编辑场所
  router.post('/venue', async (ctx, next) => {
    let item = {
      ...ctx.request.body
    };
    
    if (item.id) {
      let _item = await db.Venue.findOne({
        where: {
          id: item.id
        }
      });
      Object.assign(_item, item)
      //_item.updateUserId = ctx.params.user.id;
      await _item.save();
      item = _item;
    } else {
      delete item.id;
      // item.createUserId = ctx.params.user.id;
      // item.updateUserId = ctx.params.user.id;
      item.rank = item.rank ? item.rank : new Date().getTime();
      item.VenueStatic={
        visitCount:0,
        likeCount:0
      }
      item = await db.Venue.create(item,{
        include:[db.VenueStatic]
      })
    }
    ctx.body = result.success(item);
    return next()
  })

  // //删除场所
  // router.delete('/sport/venue',async(ctx,next)=>{
  //   //checkLogin(ctx)
  //   let id = ctx.request.query.id;
  //   let item = await db.Venue.findByPk(id);
  //   await item.destroy()
  //   ctx.body = result.success();
  //   return next();
  // })

}