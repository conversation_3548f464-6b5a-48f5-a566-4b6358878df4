import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = '服务器内部错误';
    let code = -100;

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const responseObj = exceptionResponse as any;
        message = responseObj.message || exception.message;
        code = responseObj.code || -100;
      }
    } else if (exception instanceof Error) {
      message = exception.message;
    }

    // 如果是自定义错误（有code字段），使用自定义格式
    if (exception && typeof exception === 'object' && 'code' in exception) {
      const customError = exception as any;
      return response.status(HttpStatus.OK).json({
        code: customError.code || -100,
        msg: customError.message || message,
        data: null
      });
    }

    response.status(HttpStatus.OK).json({
      code: code,
      msg: message,
      data: null
    });
  }
} 