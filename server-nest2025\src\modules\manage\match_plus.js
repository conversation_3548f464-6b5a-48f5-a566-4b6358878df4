const {parseQueryPaging,parseQueryParam} = require("../utils/funs.js")
module.exports = function (router, db, result) {


  router.get('/sport/match/query', async (ctx, next) => {
    let paging = parseQueryPaging(ctx.request.query);
    let where = parseQueryParam(ctx.request.query);
    let where_match = null;
    let where_matchClass = null;
    let where_league = null;
    let where_sport = null;
    let where_team = null;
    if (where.SportId) {
      where_sport = {
        id: where.SportId
      }
    }
    if (where.MatchClassId) {
      where_matchClass = {
        id: where.matchClassId
      }
    }
    if (where.LeagueId) {
      where_league = {
        id: where.LeagueId
      }
    }
    if (where.name) {
      where_match = {
        name: where.name
      }
    }
    if (where.state) {
      where_match = {
        state: where.state
      }
    }
    if (where.beginDate) {
      where_match = {
        beginDate: where.beginDate
      }
    }
    if (where.endDate) {
      where_match = {
        endDate: where.endDate
      }
    }
    if (where.teamName) {
      where_team = {
        name: where.teamName
      }
    }

    let options = {
      where: where_match,
      order: [
        ['createdAt', 'DESC']
      ],
      offset: (paging.pageIndex - 1) * paging.pageSize,
      limit: paging.pageSize,
      include: [{
          model: db.MatchTeamResult,
          attributes: ['score', 'state', 'updatedAt', 'id', 'rank', 'content'],
          include: [{
            model: db.Team,
            attributes: ['id', 'name'],
            where: where_team
          }],

        },
        {
          model: db.MatchClass,
          attributes: ['name'],
          where: where_matchClass
        }, {
          model: db.League,
          attributes: ['name', 'beginDate'],
          where: where_league
        }, {
          model: db.Sport,
          attributes: [],
          where: where_sport
        }
      ]
    }

    let data = await db.Match.findAndCountAll(options)

    ctx.body = result.success({
      list: data.rows,
      total: data.count,
      pageSize: paging.pageSize,
      pageIndex: paging.pageIndex
    });
  });
  router.del('/sport/match/del', async (ctx, next) => {
    let id = ctx.request.query.id;
    await db.MatchTeamResult.destroy({
      where: {
        TeamId: id,
      }
    })
    await db.LeagueTeamResult.destroy({
      where: {
        TeamId: id,
      }
    });
    let item = await db.Team.findByPk(id);
    await item.destroy();
    ctx.body = result.success();
    next()
  })

  //hook
  //监控Match的创建，删除，state更新，
  const computeMatchTotalState = async (match) => {
    let LeagueId = match.LeagueId
    let matchs = await db.Match.findAll({
      where: {
        disabled: 0,
        LeagueId,
      }
    });
    let matchOverCount = matchs.filter(v => v.state == 2).length;
    let matchCount = matchs.length

    await db.League.update({
      matchOverCount,
      matchCount
    }, {
      where: {
        id: LeagueId
      }
    });
  }


  db.Match.afterDestroy(async (match, options) => {
    await computeMatchTotalState(match)
  })
  db.Match.afterBulkDestroy(async (match, options) => {
    await computeMatchTotalState(match)
  })
  db.Match.afterCreate(async (match, options) => {
    await computeMatchTotalState(match)
  })

  db.Match.afterUpdate(async (match, options) => {
    if (!options.fields.find(v => (v == 'state' || v == 'disabled'))) {
      return;
    }
    await computeMatchTotalState(match)
  })
  //!!save触发了update
  // db.Match.afterSave(async (match, options) => {
  //   if (!options.fields.find(v => (v == 'state' || v == 'disabled'))) {
  //     return;
  //   }
  //   computeMatchTotalState(match)
  // })
  //监控MatchTeamResult
  //计算league中team总得分
  db.MatchTeamResult.afterUpdate('sumTeamTotalScore', async (result, options) => {
    if (!options.fields.find(v => v == 'score')) {
      return;
    }
    const match = await db.Match.findOne({
      where: {
        id: result.MatchId
      },
      include: [{
        model: db.League,
        attributes: ['id'],
        include: [{
          model: db.Match,
          where: {
            disabled: 0,
          },
          attributes: ['id']
        }]
      }]
    })
    const matchIds = match.League.Matches.map(v => v.id);

    const totalScore = await db.MatchTeamResult.sum('score', {
      where: {
        MatchId: matchIds,
        TeamId: result.TeamId
      },

    });


    db.LeagueTeamResult.update({
      score: totalScore
    }, {
      where: {
        TeamId: result.TeamId,
        LeagueId: match.League.id
      }
    })
  })


}