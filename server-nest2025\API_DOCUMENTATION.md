# API 文档说明

## Swagger 接口文档

本项目使用 Swagger 来生成和展示 API 接口文档。

### 访问地址

启动项目后，可以通过以下地址访问 Swagger 文档：

```
http://localhost:3000/api
```

### 主要功能

1. **接口列表**：显示所有可用的 API 接口
2. **接口测试**：可以直接在页面上测试接口
3. **参数说明**：详细的参数类型、示例和说明
4. **响应示例**：接口返回数据的格式示例
5. **认证配置**：支持 JWT 认证的接口会显示认证按钮

### 认证方式

对于需要认证的接口，需要：

1. 点击右上角的 "Authorize" 按钮
2. 在 JWT 输入框中输入你的 JWT token
3. 点击 "Authorize" 确认
4. 现在可以访问需要认证的接口了

### 城市队模块 API

#### 获取城市队列表
- **GET** `/client/city-team`
- **描述**：分页获取城市队列表，支持关键词搜索、类型筛选、地区筛选等
- **参数**：
  - `page`: 页码（默认1）
  - `limit`: 每页数量（默认10）
  - `keyword`: 搜索关键词
  - `SportId`: 运动类型ID
  - `type`: 类型（0固定队，1临时队）
  - `province`: 省份
  - `city`: 城市

#### 获取城市队详情
- **GET** `/client/city-team/:id`
- **描述**：根据城市队ID获取详细信息

#### 创建城市队
- **POST** `/client/city-team`
- **描述**：创建新的城市队，需要注册用户权限
- **需要认证**：是

#### 更新城市队
- **PUT** `/client/city-team/:id`
- **描述**：更新城市队信息，只有创建者或管理员可以修改
- **需要认证**：是

#### 删除城市队
- **DELETE** `/client/city-team/:id`
- **描述**：软删除城市队，只有创建者或管理员可以删除
- **需要认证**：是

#### 设置排序
- **POST** `/client/city-team/:id/rank`
- **描述**：设置城市队的排序权重
- **需要认证**：是

#### 根据运动类型获取城市队
- **GET** `/client/city-team/sport/:sportId`
- **描述**：根据运动类型ID获取相关的城市队列表

### 使用示例

#### 创建城市队

```json
{
  "name": "北京毽球队",
  "intro": "北京地区专业的毽球队伍，欢迎加入",
  "avatar": "https://example.com/avatar.jpg",
  "content": "我们是一支专业的毽球队伍，定期组织训练和比赛...",
  "type": 0,
  "province": "北京市",
  "city": "北京市",
  "district": "朝阳区",
  "address": "朝阳区某某街道123号",
  "leader": "张三",
  "leaderLink": "13800138000",
  "images": "[\"https://example.com/img1.jpg\", \"https://example.com/img2.jpg\"]",
  "SportId": "123e4567-e89b-12d3-a456-426614174000"
}
```

#### 查询城市队列表

```
GET /client/city-team?page=1&limit=10&keyword=北京&type=0
```

### 注意事项

1. 所有需要认证的接口都需要在 Swagger 页面中先进行认证
2. 删除操作是软删除，只是设置 `disabled=1`
3. 排序功能通过 `rank` 字段实现，数值越大排序越靠前
4. 创建者和管理员有修改和删除的权限
5. 支持按地区、类型、关键词等多种方式筛选

### 错误码说明

- `200`: 成功
- `201`: 创建成功
- `400`: 参数错误
- `401`: 未授权
- `403`: 没有权限
- `404`: 资源不存在 