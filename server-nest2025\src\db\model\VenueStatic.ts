//场所-统计数据表
import {
  Column,
  DataType,
  Default,
  Model,
  Table,
  PrimaryKey,
  IsUUID
} from 'sequelize-typescript';
@Table({timestamps: false})

export default class VenueStatic extends Model {
  @PrimaryKey
  @IsUUID(4)
  @Column(DataType.UUID)
   declare id: string

    @Default(0)
    @Column
    declare visitCount:number //签到数

    @Default(0)
    @Column
    declare hotCount:number//点击数

    @Default(0)
    @Column
    declare likeCount:number//喜欢

    @Default(0)
    @Column
    declare shareCount:number//分享次数

    @Default(0)
    @Column
    declare upCount:number//赞
    
    @Default(0)
    @Column
    declare downCount:number//踩
}
