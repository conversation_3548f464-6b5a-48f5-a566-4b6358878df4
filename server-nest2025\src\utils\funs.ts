//业务通用方法
import { Op } from 'sequelize';
import * as jwt from 'jsonwebtoken';
import { ajax } from './ajax';
import * as chinaDivision from './china-division.json'

//合并target obj仅有的属性,浅合并
//for sequelize model :处理ctx.request参数生成模型时,限制提交的参数项，减小hack风险
//requestParams:ctx.request.body或ctx.request.query
//mdel:模型默认值
//needFields中的参数限制为必须存在，否则抛出异常
export function parseRequestToModel(requestParams, model = {}, needFields = []) {

  let _obj = {}
  for (var k in model) {
    if (needFields.includes(k as never) && (requestParams[k] === '' || requestParams[k] === null || requestParams[k] === undefined)) {
      throw new Error('参数缺失:' + k)
    }
    if (k in requestParams) {
      _obj[k] = requestParams[k];
    } else {
      _obj[k] = model[k]
    }
  }
  return _obj
}

export function getNameComplex(n) {
  let last = n.substr(n.length - 2)
  let complex = ''
  if (last == 'ch' || last == 'sh') {
    return n + 'es';
  }
  last = n.substr(n.length - 1)
  switch (last) {
    case 's':
    case 'z':
    case 'x':
      return n + 'es';
    case 'y':
      return n.substr(0, n.length - 1) + 'ies'
    default:
      return n + 's'
  }
}
/**
 * 分页查询参数生成器
 * @description 用于生成分页查询的where条件和分页参数
 * @param {boolean} [disabled] - 是否包含disabled=0的过滤条件
 */
export async function queryPaginDataAsync(dbTable, query: any, options: any = {}, disabled?: boolean) {

  let paging = exports.parseQueryPaging(query);
  let where = exports.parseQueryParam(query)

  if (disabled) {
    where.disabled = 0;
  }
  if (options.where) {
    Object.assign(where, options.where)
  }

  console.log(options)
  let _options = {
    order: [
      ['rank', 'DESC']
    ],
    //attributes: ['id', 'name', 'avatar', 'intro', 'extUrl'],
    offset: (paging.pageIndex - 1) * paging.pageSize,
    limit: paging.pageSize,
    raw: true,
    nest: true,
  }
  Object.assign(_options, options, { where })
  //_options = merge(_options, options); my/venues 报无限递归错误
  let data = await dbTable.findAndCountAll(_options)
  return {
    list: data.rows,
    total: data.count,
    pageSize: paging.pageSize,
    pageIndex: paging.pageIndex
  };
}

export function parseQueryParam(queryParam) {
  let where = {};
  if (queryParam.where) {
    let param = JSON.parse(queryParam.where);
    for (let k in param) {
      if (param[k] !== "" && param[k] !== undefined && param[k] !== null) {
        if (!where) {
          where = {};
        }
        if (param[k] instanceof Object) {

          for (let kk in param[k]) {
            if (param[k][kk] !== "" && param[k][kk] !== undefined && param[k][kk] !== null) {
              if (!where[k]) {
                where[k] = {}

              }
              where[k][Op[kk]] = kk == "like" ? ("%" + param[k][kk] + "%") : (param[k][kk])
            }

          }


        } else {
          where[k] = param[k]
          // {
          //     and: param[k]
          // };
        }
      }
    }
  }
  return where
}

export function parseQueryPaging(queryParam) {
  let paging = {
    pageIndex: 1,
    pageSize: 20,
  }
  if (!(queryParam.pageIndex >= 1)) {
    paging.pageIndex = 1;
  }
  if (!(queryParam.pageIndex >= 0)) {
    paging.pageSize = 20;
  }
  paging.pageIndex = parseInt(queryParam.pageIndex) || 1;
  paging.pageSize = parseInt(queryParam.pageSize) || 20;
  return paging;
}

export function checkLogin(ctx) {
  if (!ctx.params.user || !ctx.params.user.id) {
    throw new Error('请选登录')
  }
}

// 生成token 返回token和过期时间（时间戳)
// obj 可为{id:'',openid:''}
// expire过期时间，秒
// code 密钥
export function newAccountToken(obj, expire = 3600, code = "windbell2") {
  const token = jwt.sign(obj, code, {
    expiresIn: expire + 's' //过期时间设置为60妙。那么decode这个token的时候得到的过期时间为 : 创建token的时间 +　设置的值
  });
  //tokenPool.push(token)
  return { token, expire: new Date().getTime() + expire * 1000 };
}

//从source中选择keys中定义的属性，返回一个新对象，支持深拷贝
//如果source中没有keys中定义的属性，则不返回
export function pickDefinedFields<T>(source: Partial<T>): Partial<T> {
  if (Array.isArray(source)) {
    return source
      .map(item => pickDefinedFields(item))
      .filter(item => item !== undefined) as unknown as Partial<T>;
  } else if (source && typeof source === 'object') {
    const result: Partial<T> = {};
    (Object.keys(source) as (keyof T)[]).forEach(key => {
      const value = source[key];
      if (value !== undefined) {
        if (typeof value === 'object' && value !== null) {
          result[key] = pickDefinedFields(value) as any;
        } else {
          result[key] = value;
        }
      }
    });
    return result;
  }
  return source;
}

//通地腾讯地图api 根据坐标，返回省市区
export async function getLocByLL(lat,lon){
	let res:any = await ajax({url:'https://apis.map.qq.com/ws/geocoder/v1/?location='+lat+','+lon+"&key=OC7BZ-GZVRJ-ADJFV-KVJKB-2AAGK-BDFLM"})
	let province = res.result.address_component.province.replace(/省$/,'').replace(/市$/,'')
	let city = res.result.address_component.city.replace(/市$/,'')
	let district = res.result.address_component.district;
	return {
		province,
		city,
		district
	}
}

/**
 * 获取城市编码范围，如果code省直辖县，max,min为code本身，
 * @param code 城市编码
 * @returns 城市编码范围{max:最大编码,min:最小编码}
 */
export function getCityDivisionRange(cityCode:number) {
  let max:number,min:number;
  //判断是否是省直辖县
  let is直辖县 = false;
  const province = chinaDivision.data.find(province=>{
    const city = province.c.find(city=>{
       if(city.v==cityCode.toString()){
        return true;
       }
    });
    return city;
  });

  if(cityCode%100!=0){//尾数为00 非标准高市
    if(province){//是县
      if(province.c.length==0 || province?.c[0].n==province.n){
        is直辖县 = true;
      }
    }
  }


  if(is直辖县){ 
    max = cityCode;
    min = cityCode;
  }else{//如果是直辖市（县），则返回直辖市的范围
    min = Math.floor(cityCode / 100) * 100;
    max = min + 99;
  }
  //console.log('getCityDivisionRange',cityCode,max,min);
  return {
    max,
    min,
  }
}
